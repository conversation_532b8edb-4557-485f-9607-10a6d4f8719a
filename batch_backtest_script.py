#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批量回测脚本 - 对decoded_expressions-6.txt中的因子进行批量回测
使用jy_worldquant/core文件夹中的回测功能
"""

import os
import sys
import logging
import configparser
from datetime import datetime
from typing import List, Tuple

# 添加jy_worldquant目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'jy_worldquant'))

from core import AlphaService, SimulationService
from common_config import CommonConfig
from common_auth import BrainAuth
from db_utils import setup_database


class BatchBacktestRunner:
    """批量回测运行器"""
    
    def __init__(self, factors_file: str, config_file: str = 'jy_worldquant/config.ini'):
        """
        初始化批量回测运行器
        
        Args:
            factors_file: 因子文件路径
            config_file: 配置文件路径
        """
        self.factors_file = factors_file
        self.config_file = config_file
        self.logger = None
        self.common_config = None
        self.db_service = None
        self.auth_service = None
        self.alpha_service = None
        self.simulation_service = None
        
    def setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 设置日志文件名
        log_file = os.path.join(
            log_dir, 
            f"batch_backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        )
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    
    def load_configuration(self):
        """加载配置"""
        self.logger.info(f"加载配置文件: {self.config_file}")
        
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        config_parser = configparser.ConfigParser()
        config_parser.read(self.config_file, encoding='utf-8')
        
        # 创建CommonConfig对象
        config_dict = {}
        for section in config_parser.sections():
            for key, value in config_parser.items(section):
                # 处理布尔值
                if value.lower() in ['true', 'false']:
                    config_dict[key] = value.lower() == 'true'
                # 处理数字
                elif value.replace('.', '').replace('-', '').isdigit():
                    if '.' in value:
                        config_dict[key] = float(value)
                    else:
                        config_dict[key] = int(value)
                else:
                    config_dict[key] = value
        
        self.common_config = CommonConfig(**config_dict)
        self.logger.info("配置加载完成")
    
    def setup_database(self):
        """初始化数据库"""
        self.logger.info("初始化数据库")
        self.db_service = setup_database(self.common_config)
        self.logger.info("数据库初始化完成")
    
    def setup_auth_service(self):
        """初始化认证服务"""
        self.logger.info("初始化认证服务")
        self.auth_service = BrainAuth()
        
        # 验证登录
        session = self.auth_service.login()
        if not session:
            raise Exception("登录失败，请检查凭证文件")
        
        self.logger.info("认证服务初始化完成")
    
    def setup_services(self):
        """初始化业务服务"""
        self.logger.info("初始化业务服务")
        
        # 初始化Alpha服务
        self.alpha_service = AlphaService(
            config=self.common_config,
            auth_service=self.auth_service,
            db_service=self.db_service
        )
        
        # 初始化仿真服务
        self.simulation_service = SimulationService(
            config=self.common_config,
            auth_service=self.auth_service,
            db_service=self.db_service
        )
        
        self.logger.info("业务服务初始化完成")
    
    def load_factors(self) -> List[Tuple[str, int]]:
        """
        从文件加载因子表达式
        
        Returns:
            因子列表，每个元素为 (expression, decay) 元组
        """
        self.logger.info(f"从文件加载因子: {self.factors_file}")
        
        if not os.path.exists(self.factors_file):
            raise FileNotFoundError(f"因子文件不存在: {self.factors_file}")
        
        factors = []
        with open(self.factors_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # 跳过空行和注释行
                    # 使用配置中的decay值
                    factors.append((line, self.common_config.decay))
        
        self.logger.info(f"成功加载 {len(factors)} 个因子表达式")
        return factors
    
    def run_backtest(self):
        """运行批量回测"""
        try:
            # 1. 初始化系统
            self.setup_logging()
            self.load_configuration()
            self.setup_database()
            self.setup_auth_service()
            self.setup_services()
            
            # 2. 加载因子
            factors = self.load_factors()
            
            if not factors:
                self.logger.warning("没有找到任何因子，退出")
                return
            
            # 3. 过滤已回测的因子（如果启用了数据库）
            if self.db_service:
                self.logger.info("过滤已回测的因子")
                factors = self.alpha_service.filter_new_alphas(factors)
                self.logger.info(f"过滤后剩余 {len(factors)} 个新因子需要回测")
            
            if not factors:
                self.logger.info("所有因子都已回测过，无需重复回测")
                return
            
            # 4. 开始批量回测
            self.logger.info(f"开始批量回测 {len(factors)} 个因子")
            self.logger.info(f"批次大小: {self.common_config.batch_size}")
            
            total_processed = 0
            for progress in self.simulation_service.batch_simulate(factors):
                self.logger.info(f"回测进度: {progress}%")
                
                # 计算已处理的因子数量
                current_processed = int(len(factors) * progress / 100)
                if current_processed > total_processed:
                    total_processed = current_processed
                    self.logger.info(f"已处理 {total_processed}/{len(factors)} 个因子")
            
            self.logger.info("批量回测完成！")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"批量回测失败: {str(e)}")
            else:
                print(f"批量回测失败: {str(e)}")
            raise


def main():
    """主函数"""
    # 因子文件路径
    factors_file = "decoded_expressions-6.txt"
    
    # 检查文件是否存在
    if not os.path.exists(factors_file):
        print(f"错误: 因子文件不存在: {factors_file}")
        return
    
    # 创建并运行批量回测器
    runner = BatchBacktestRunner(factors_file)
    runner.run_backtest()


if __name__ == "__main__":
    main()
