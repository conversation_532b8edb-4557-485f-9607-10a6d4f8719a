# 第2批次：成长性与盈利质量Alpha (101-200)
# 基于经济学理论：成长性指标与盈利质量指标的回归残差分析

# Alpha 101-110: 不同期间EPS预期的成长性分析
# 经济学逻辑：不同期间EPS预期的变化反映成长性，与当期盈利质量的关系体现成长的可持续性
residual = ts_regression(ts_zscore(anl14_mean_eps_fp2,500), ts_zscore(anl14_mean_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp3,500), ts_zscore(anl14_mean_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp4,500), ts_zscore(anl14_mean_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp5,500), ts_zscore(anl14_mean_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_eps_fp2,500), ts_zscore(anl14_median_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_eps_fp3,500), ts_zscore(anl14_median_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_eps_fp4,500), ts_zscore(anl14_median_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_eps_fp5,500), ts_zscore(anl14_median_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_eps_fp2,500), ts_zscore(anl14_high_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_low_eps_fp2,500), ts_zscore(anl14_low_eps_fp1,500),500); residual/ts_std_dev(residual,500)

# Alpha 111-120: 收入成长性与盈利质量关系
# 经济学逻辑：收入增长反映业务扩张能力，与ROE/ROA的关系体现增长的盈利效率
residual = ts_regression(ts_zscore(anl14_mean_revenue_fp2,500), ts_zscore(anl14_mean_revenue_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp3,500), ts_zscore(anl14_mean_revenue_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp4,500), ts_zscore(anl14_mean_revenue_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp5,500), ts_zscore(anl14_mean_revenue_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp2,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp3,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp4,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp2,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp3,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp4,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

# Alpha 121-130: 成长性与现金流质量关系
# 经济学逻辑：高成长企业的现金流质量更为重要，成长与现金流的关系体现增长的可持续性
residual = ts_regression(ts_zscore(anl14_mean_eps_fp2,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp3,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp4,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp2,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp3,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp4,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp2,500), ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp3,500), ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp2,500), ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp3,500), ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500),500); residual/ts_std_dev(residual,500)

# Alpha 131-140: ROE/ROA成长性分析
# 经济学逻辑：ROE和ROA的时间序列变化反映盈利效率的改善趋势
residual = ts_regression(ts_zscore(anl14_mean_roe_fp2,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roe_fp3,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roe_fp4,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roe_fp5,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roa_fp2,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roa_fp3,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roa_fp4,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roa_fp5,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_roe_fp2,500), ts_zscore(anl14_median_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_roa_fp2,500), ts_zscore(anl14_median_roa_fp1,500),500); residual/ts_std_dev(residual,500)

# Alpha 141-150: 成长性与毛利率关系
# 经济学逻辑：成长性企业的毛利率变化反映定价能力和成本控制能力的演变
residual = ts_regression(ts_zscore(anl14_mean_revenue_fp2,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp3,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp4,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp2,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp3,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp4,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_revenue_fp2,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_revenue_fp3,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_eps_fp2,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_eps_fp3,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_median,500),500); residual/ts_std_dev(residual,500)

# Alpha 151-160: 成长性与EBITDA关系
# 经济学逻辑：EBITDA增长反映经营层面的盈利改善，与收入/EPS增长的关系体现运营杠杆效应
residual = ts_regression(ts_zscore(anl14_mean_revenue_fp2,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp3,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp4,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp2,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp3,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp4,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_revenue_fp2,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_revenue_fp3,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_eps_fp2,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_eps_fp3,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

# Alpha 161-170: 成长性与净债务关系
# 经济学逻辑：成长性企业的债务水平变化反映融资需求和财务策略
residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp2,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp3,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp4,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp5,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp2,500), ts_zscore(anl14_mean_revenue_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp3,500), ts_zscore(anl14_mean_revenue_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp4,500), ts_zscore(anl14_mean_revenue_fp4,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp2,500), ts_zscore(anl14_mean_eps_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp3,500), ts_zscore(anl14_mean_eps_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp4,500), ts_zscore(anl14_mean_eps_fp4,500),500); residual/ts_std_dev(residual,500)

# Alpha 171-180: 成长性预期的一致性分析
# 经济学逻辑：分析师对成长性预期的一致性反映市场对企业前景的共识程度
residual = ts_regression(ts_zscore(anl14_stddev_eps_fp2,500), ts_zscore(anl14_mean_eps_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_eps_fp3,500), ts_zscore(anl14_mean_eps_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_eps_fp4,500), ts_zscore(anl14_mean_eps_fp4,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_eps_fp5,500), ts_zscore(anl14_mean_eps_fp5,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_revenue_fp2,500), ts_zscore(anl14_mean_revenue_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_revenue_fp3,500), ts_zscore(anl14_mean_revenue_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_revenue_fp4,500), ts_zscore(anl14_mean_revenue_fp4,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_revenue_fp5,500), ts_zscore(anl14_mean_revenue_fp5,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_roe_fp2,500), ts_zscore(anl14_mean_roe_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_roa_fp2,500), ts_zscore(anl14_mean_roa_fp2,500),500); residual/ts_std_dev(residual,500)

# Alpha 181-190: 高低预期差异与成长性关系
# 经济学逻辑：分析师预期的高低差异反映不确定性，与成长性的关系体现风险收益特征
residual = ts_regression(ts_zscore(anl14_high_eps_fp1,500), ts_zscore(anl14_low_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_eps_fp2,500), ts_zscore(anl14_low_eps_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_eps_fp3,500), ts_zscore(anl14_low_eps_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_revenue_fp1,500), ts_zscore(anl14_low_revenue_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_revenue_fp2,500), ts_zscore(anl14_low_revenue_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_revenue_fp3,500), ts_zscore(anl14_low_revenue_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_roe_fp1,500), ts_zscore(anl14_low_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_roe_fp2,500), ts_zscore(anl14_low_roe_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_roa_fp1,500), ts_zscore(anl14_low_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_roa_fp2,500), ts_zscore(anl14_low_roa_fp2,500),500); residual/ts_std_dev(residual,500)

# Alpha 191-200: 成长性与分析师数量关系
# 经济学逻辑：关注度高的成长股通常有更多分析师覆盖，分析师数量与成长性的关系体现市场关注度
residual = ts_regression(ts_zscore(anl14_numofests_eps_fp1,500), ts_zscore(anl14_mean_eps_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_eps_fp2,500), ts_zscore(anl14_mean_eps_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_eps_fp3,500), ts_zscore(anl14_mean_eps_fp4,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_revenue_fp1,500), ts_zscore(anl14_mean_revenue_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_revenue_fp2,500), ts_zscore(anl14_mean_revenue_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_revenue_fp3,500), ts_zscore(anl14_mean_revenue_fp4,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_roe_fp1,500), ts_zscore(anl14_mean_roe_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_roe_fp2,500), ts_zscore(anl14_mean_roe_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_roa_fp1,500), ts_zscore(anl14_mean_roa_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_roa_fp2,500), ts_zscore(anl14_mean_roa_fp3,500),500); residual/ts_std_dev(residual,500)
