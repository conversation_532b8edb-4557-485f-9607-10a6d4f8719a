#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试SimulationService集成的脚本
"""

import sys
import logging
sys.path.append('.')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test')

def test_simulation_service():
    """测试SimulationService的基本功能"""
    try:
        from pipeline_config_and_data_structures_v2 import create_simulation_config
        from jy_worldquant.core.simulation_service import SimulationService

        logger.info("🔧 创建配置和服务...")
        config, auth_service, db_service = create_simulation_config('config_v2.ini')
        simulation_service = SimulationService(config, auth_service, db_service)

        logger.info("✅ 服务创建成功")
        logger.info(f"服务名称: {simulation_service.get_service_name()}")

        # 测试一个简单的Alpha列表
        test_alphas = [
            ("rank(close)", 0),
            ("ts_mean(close, 20)", 0)
        ]

        logger.info(f"🧪 测试仿真 {len(test_alphas)} 个Alpha...")

        # 注意：这里会实际发送请求到WorldQuant Brain API
        # 在生产环境中要小心使用
        successful_alphas = simulation_service.simulate_alphas(test_alphas)

        logger.info(f"✅ 仿真完成")
        logger.info(f"成功的Alpha数量: {len(successful_alphas)}")

        for i, (expr, decay) in enumerate(successful_alphas):
            logger.info(f"  {i+1}. {expr} (decay={decay})")

        return True

    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_authentication():
    """测试认证功能"""
    try:
        from jy_worldquant.common_auth import BrainAuth

        logger.info("🔐 测试认证...")
        username, password = BrainAuth.load_credentials()
        logger.info(f"用户名: {username}")

        # 实际登录测试
        session = BrainAuth.login()
        if session:
            logger.info("✅ 认证成功")
            return True
        else:
            logger.error("❌ 认证失败")
            return False

    except Exception as e:
        logger.error(f"❌ 认证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试SimulationService集成")
    print("=" * 60)

    # 首先测试认证
    auth_success = test_authentication()

    if auth_success:
        print("\n" + "=" * 60)
        # 如果认证成功，测试仿真服务
        # 注意：这会实际发送请求，请谨慎使用
        choice = input("认证成功！是否继续测试仿真服务？(y/N): ").strip().lower()
        if choice == 'y':
            test_simulation_service()
        else:
            print("跳过仿真服务测试")
    else:
        print("认证失败，跳过仿真服务测试")

    print("\n🏁 测试完成")