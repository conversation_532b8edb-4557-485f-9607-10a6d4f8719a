# 第1批次：盈利能力与估值关系Alpha (1-100)
# 基于经济学理论：盈利能力指标与估值指标的回归残差分析

# Alpha 1-10: EPS与每股净资产关系
# 经济学逻辑：EPS反映盈利能力，每股净资产反映账面价值，两者关系体现价值创造能力
residual = ts_regression(ts_zscore(pv87_ann_matrix_eps_gaap_estimate_mean,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_eps_normalized_estimate_mean,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp1,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_eps_fp1,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_eps_fp1,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_low_eps_fp1,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp2,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp3,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp4,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_eps_fp5,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_mean,500),500); residual/ts_std_dev(residual,500)

# Alpha 11-20: 净利润与EBITDA关系
# 经济学逻辑：净利润是最终盈利，EBITDA反映经营盈利能力，两者差异体现财务和税务影响
residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_mean,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_median,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_high,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_low,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_normalized_estimate_mean,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_normalized_estimate_median,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_normalized_estimate_high,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_normalized_estimate_low,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_std,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_normalized_estimate_std,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 21-30: EBIT与利息费用关系
# 经济学逻辑：EBIT与利息费用比率反映利息覆盖能力，是重要的偿债能力指标
residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_mean,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_median,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_high,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_low,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_std,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_std,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 31-40: 毛利率与收入关系
# 经济学逻辑：毛利率反映定价能力和成本控制，与收入规模的关系体现规模经济效应
residual = ts_regression(ts_zscore(pv87_ann_matrix_gross_margin_estimate_mean,500), ts_zscore(pv87_ann_matrix_revenue_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_gross_margin_estimate_median,500), ts_zscore(pv87_ann_matrix_revenue_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_gross_margin_estimate_high,500), ts_zscore(pv87_ann_matrix_revenue_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_gross_margin_estimate_low,500), ts_zscore(pv87_ann_matrix_revenue_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_gross_margin_estimate_std,500), ts_zscore(pv87_ann_matrix_revenue_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_revenue_fp1,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_revenue_fp1,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_revenue_fp1,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_low_revenue_fp1,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_revenue_fp1,500), ts_zscore(pv87_ann_matrix_gross_margin_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 41-50: ROE与ROA关系
# 经济学逻辑：ROE和ROA的差异反映财务杠杆效应，体现资本结构对股东回报的影响
residual = ts_regression(ts_zscore(anl14_mean_roe_fp1,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_roe_fp1,500), ts_zscore(anl14_median_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_roe_fp1,500), ts_zscore(anl14_high_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_low_roe_fp1,500), ts_zscore(anl14_low_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_roe_fp1,500), ts_zscore(anl14_stddev_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roe_fp2,500), ts_zscore(anl14_mean_roa_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roe_fp3,500), ts_zscore(anl14_mean_roa_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roe_fp4,500), ts_zscore(anl14_mean_roa_fp4,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_roe_fp5,500), ts_zscore(anl14_mean_roa_fp5,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_roe_fp2,500), ts_zscore(anl14_median_roa_fp2,500),500); residual/ts_std_dev(residual,500)

# Alpha 51-60: 现金流与净利润关系
# 经济学逻辑：现金流与净利润的差异反映盈利质量，现金流更真实反映企业经营状况
residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_median,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_high,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_low,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_std,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_median,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_high,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_low,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_std,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 61-70: 自由现金流与经营现金流关系
# 经济学逻辑：自由现金流扣除资本支出，反映企业真正可分配的现金，与经营现金流差异体现投资强度
residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_median,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_high,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_low,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_std,500), ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_median,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_median,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_std,500), ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 71-80: 净债务与EBITDA关系
# 经济学逻辑：净债务与EBITDA比率是重要的杠杆指标，反映企业偿债能力和财务风险
residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_mean,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_median,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_high,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_low,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_low_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp2,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp3,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

# Alpha 81-90: 有效税率与税前利润关系
# 经济学逻辑：有效税率反映税务效率，与税前利润的关系体现税务管理能力
residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_mean,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_median,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_high,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_low,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_std,500), ts_zscore(pv87_ann_matrix_ebt_gaap_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_mean,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_median,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_high,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_low,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_effective_tax_rate_estimate_std,500), ts_zscore(pv87_ann_matrix_ebt_normalized_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 91-100: 分析师预期一致性与盈利指标关系
# 经济学逻辑：分析师预期的标准差反映不确定性，与盈利指标的关系体现业绩可预测性
residual = ts_regression(ts_zscore(pv87_ann_matrix_eps_gaap_estimate_std,500), ts_zscore(pv87_ann_matrix_eps_gaap_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_revenue_estimate_std,500), ts_zscore(pv87_ann_matrix_revenue_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_std,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_std,500), ts_zscore(pv87_ann_matrix_ebit_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_std,500), ts_zscore(pv87_ann_matrix_net_income_gaap_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_eps_fp1,500), ts_zscore(anl14_mean_eps_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_revenue_fp1,500), ts_zscore(anl14_mean_revenue_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_roe_fp1,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_roa_fp1,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_eps_normalized_estimate_std,500), ts_zscore(pv87_ann_matrix_eps_normalized_estimate_mean,500),500); residual/ts_std_dev(residual,500)
