#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完全复现jy_worldquant项目的AlphaMachine流水线
使用原项目的架构和服务
"""

import os
import sys
import logging
import threading
import configparser
from datetime import datetime, timedelta
from typing import Optional, List, Tuple, Any

# 添加jy_worldquant项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jy_worldquant'))

# 导入jy_worldquant项目的核心模块
from jy_worldquant.core import AlphaService, SimulationService, SubmissionService
from jy_worldquant.common_config import CommonConfig
from jy_worldquant.common_auth import BrainAuth
from jy_worldquant.db_utils import setup_database

# 从配置文件中导入
from pipeline_config_and_data_structures_v2 import DEFAULT_FACTORS_FILE


class ReplicatedAlphaMachine:
    """完全复现jy_worldquant项目的Alpha机器"""
    
    def __init__(self, config_file: str = 'config_v2.ini', factors_file: str = None):
        """
        初始化Alpha机器
        
        Args:
            config_file: 配置文件路径
            factors_file: 因子文件路径
        """
        self.config_file = config_file
        self.factors_file = factors_file or DEFAULT_FACTORS_FILE
        self.logger = None
        self.config_parser = None
        self.common_config = None
        self.db_service = None
        self.auth_service = None
        
        # 服务对象
        self.alpha_service = None
        self.simulation_service = None
        self.submission_service = None
        
        # 异步任务列表
        self.async_tasks = []
        
    def initialize(self) -> bool:
        """
        初始化系统组件
        
        Returns:
            是否初始化成功
        """
        try:
            # 1. 设置日志系统
            self._setup_logging()
            
            # 2. 加载配置
            self._load_configuration()
            
            # 3. 初始化数据库
            self._setup_database()
            
            # 4. 初始化认证服务
            self._setup_auth_service()
            
            # 5. 初始化业务服务
            self._setup_services()
            
            self.logger.info("Alpha机器初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Alpha机器初始化失败: {str(e)}")
            else:
                print(f"Alpha机器初始化失败: {str(e)}")
            return False
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 设置日志文件名
        log_file = os.path.join(
            log_dir, 
            f"replicated_alpha_machine_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"
        )
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    
    def _load_configuration(self):
        """加载配置文件"""
        self.logger.info("加载配置文件")
        
        self.config_parser = configparser.ConfigParser()
        self.config_parser.read(self.config_file, encoding='utf-8')
        
        # 创建通用配置对象
        self.common_config = CommonConfig.from_config(self.config_parser)
        self.common_config.log_parameters()
    
    def _setup_database(self):
        """设置数据库服务"""
        self.logger.info("初始化数据库服务")
        
        self.db_service = setup_database(self.config_parser)
        if not self.db_service:
            self.logger.warning("数据库连接失败，将不会保存回测记录")
    
    def _setup_auth_service(self):
        """设置认证服务"""
        self.logger.info("初始化认证服务")
        self.auth_service = BrainAuth()
    
    def _setup_services(self):
        """设置业务服务"""
        self.logger.info("初始化业务服务")
        
        # Alpha服务
        self.alpha_service = AlphaService(
            config=self.common_config,
            auth_service=self.auth_service,
            db_service=self.db_service
        )
        
        # 仿真服务
        self.simulation_service = SimulationService(
            config=self.common_config,
            auth_service=self.auth_service,
            db_service=self.db_service
        )
        
        # 提交服务
        self.submission_service = SubmissionService(
            config=self.common_config,
            auth_service=self.auth_service,
            db_service=self.db_service
        )
    
    def get_execution_dates(self) -> tuple[str, str]:
        """
        获取执行日期
        
        Returns:
            (开始日期, 结束日期) 元组
        """
        if self.config_parser.getboolean('dates', 'use_dynamic_dates'):
            current_date = datetime.today()
            end_date = current_date.strftime("%m-%d")
            start_date = (current_date - timedelta(days=1)).strftime("%m-%d")
        else:
            start_date = self.config_parser.get('dates', 'start_date')
            end_date = self.config_parser.get('dates', 'end_date')
        
        self.logger.info(f"执行日期范围: {start_date} 到 {end_date}")
        return start_date, end_date
    
    def run_alpha_generation_and_simulation(self) -> bool:
        """
        运行Alpha生成和仿真流程
        
        Returns:
            是否成功
        """
        if not self.common_config.is_simulate:
            self.logger.info("跳过Alpha生成和仿真流程")
            return True
        
        self.logger.info("开始Alpha生成和仿真流程")
        
        try:
            # 1. 生成Alpha
            alpha_list = self._generate_alphas()
            if not alpha_list:
                self.logger.warning("未生成任何Alpha")
                return False
            
            # 2. 批量仿真
            self._run_batch_simulation(alpha_list)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Alpha生成和仿真流程失败: {str(e)}")
            return False
    
    def _generate_alphas(self) -> list:
        """
        生成Alpha表达式
        
        Returns:
            Alpha列表
        """
        self.logger.info("开始生成Alpha表达式")
        
        # 从文件生成Alpha
        alpha_list = self.alpha_service.create_alpha_from_file(self.factors_file)
        
        # 过滤已回测的Alpha
        if self.db_service:
            self.logger.info(f"生成了 {len(alpha_list)} 个Alpha，开始过滤已回测的Alpha")
            alpha_list = self.alpha_service.filter_new_alphas(alpha_list)
            self.logger.info(f"过滤后剩余 {len(alpha_list)} 个未回测的Alpha")
        
        return alpha_list
    
    def _run_batch_simulation(self, alpha_list: list):
        """
        运行批量仿真
        
        Args:
            alpha_list: Alpha列表
        """
        start_date, end_date = self.get_execution_dates()
        
        # 批量仿真
        self.logger.info(f"开始批量仿真 {len(alpha_list)} 个Alpha")
        for progress in self.simulation_service.batch_simulate(alpha_list):
            self.logger.info(f"仿真进度: {progress}%")
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("开始清理资源")
        
        # 等待异步任务完成
        if self.async_tasks:
            self.logger.info(f"等待 {len(self.async_tasks)} 个异步任务完成")
            for task in self.async_tasks:
                task.join()
            self.logger.info("所有异步任务已完成")
        
        # 关闭数据库连接
        if self.db_service:
            # 检查是否是异步数据库服务
            if hasattr(self.db_service, 'wait_for_completion'):
                self.logger.info("等待异步数据库操作完成")
                self.db_service.wait_for_completion()
            
            self.db_service.close()
            self.logger.info("数据库连接已关闭")
        
        self.logger.info("资源清理完成")
    
    def run(self) -> bool:
        """
        运行完整的Alpha机器流程
        
        Returns:
            是否成功
        """
        if not self.initialize():
            return False
        
        try:
            # 运行Alpha生成和仿真流程
            success = self.run_alpha_generation_and_simulation()
            
            return success
            
        except Exception as e:
            self.logger.error(f"Alpha机器运行失败: {str(e)}")
            return False
            
        finally:
            self.cleanup()
