# 批量回测使用说明

## 概述

本文档说明如何使用创建的脚本对 `decoded_expressions-6.txt` 文件中的因子进行批量回测。

## 文件说明

### 1. 核心文件
- `simple_batch_backtest.py` - 简化版批量回测脚本（推荐使用）
- `batch_backtest_script.py` - 完整版批量回测脚本
- `decoded_expressions-6.txt` - 包含642个因子表达式的文件

### 2. 配置文件
- `jy_worldquant/brain_credentials.txt` - WorldQuant BRAIN登录凭证
- `backtest_config.ini` - 回测配置文件
- `jy_worldquant/config.ini` - 原始配置文件

### 3. 核心功能模块（jy_worldquant/core/）
- `simulation_service.py` - 仿真服务，负责Alpha的回测和仿真
- `alpha_service.py` - Alpha服务，负责Alpha的生成、筛选和管理
- `alpha_factory.py` - Alpha表达式工厂类
- `base_service.py` - 基础服务类

## 使用步骤

### 1. 环境准备

确保已安装必要的Python包：
```bash
cd jy_worldquant
pip install -r requirements.txt
```

### 2. 凭证配置

凭证文件 `jy_worldquant/brain_credentials.txt` 已经配置好您的账号信息：
```json
["<EMAIL>", "Kyz417442"]
```

### 3. 运行批量回测

#### 方法一：使用简化版脚本（推荐）
```bash
python simple_batch_backtest.py
```

#### 方法二：使用完整版脚本
```bash
python batch_backtest_script.py
```

### 4. 监控进度

脚本运行时会：
- 在控制台显示实时进度
- 在 `logs/` 目录下生成详细日志文件
- 将回测结果保存到数据库

## 回测配置说明

### 主要参数
- **批次大小**: 50个因子/批次（可在脚本中调整）
- **回测区域**: USA
- **股票池**: TOP3000
- **衰减参数**: 6
- **中性化**: SUBINDUSTRY
- **数据集**: model26

### 回测流程
1. **加载因子**: 从 `decoded_expressions-6.txt` 读取642个因子表达式
2. **过滤重复**: 检查数据库，跳过已回测的因子
3. **分批处理**: 每批处理50个因子，避免系统过载
4. **结果保存**: 回测结果自动保存到SQLite数据库

## 核心功能解析

### 1. SimulationService（仿真服务）
- **batch_simulate()**: 批量仿真Alpha，返回进度
- **simulate_alphas()**: 仿真Alpha列表
- **_multi_simulate()**: 多线程仿真Alpha池

### 2. AlphaService（Alpha服务）
- **filter_new_alphas()**: 过滤已回测的Alpha
- **create_alpha_from_datafields()**: 从数据字段创建Alpha表达式
- **get_alphas()**: 获取符合条件的Alpha列表

### 3. AlphaFactory（Alpha工厂）
- **create_first_order_alphas()**: 创建一阶Alpha表达式
- **create_second_order_alphas()**: 创建二阶Alpha表达式
- **create_trade_when_operations()**: 创建交易条件操作

## 输出结果

### 1. 日志文件
位置：`logs/simple_backtest_YYYYMMDD_HHMMSS.log`
包含：
- 详细的执行过程
- 错误信息和警告
- 进度统计

### 2. 数据库
位置：`data/backtest_results.db`
包含：
- 回测结果数据
- Alpha表达式信息
- 性能指标

### 3. 控制台输出
实时显示：
- 当前处理的批次
- 完成进度百分比
- 关键状态信息

## 注意事项

1. **网络连接**: 确保网络连接稳定，回测需要与WorldQuant BRAIN服务器通信
2. **账号限制**: 注意WorldQuant BRAIN的API调用限制
3. **资源使用**: 批量回测会消耗较多系统资源，建议在性能较好的机器上运行
4. **中断恢复**: 如果中断，重新运行脚本会自动跳过已完成的因子
5. **结果验证**: 建议定期检查数据库中的回测结果

## 故障排除

### 常见问题
1. **登录失败**: 检查凭证文件格式和账号密码
2. **网络超时**: 检查网络连接，可能需要重试
3. **内存不足**: 减少批次大小（batch_size）
4. **权限错误**: 确保有写入logs和data目录的权限

### 日志分析
查看日志文件了解详细错误信息：
```bash
tail -f logs/simple_backtest_*.log
```

## 扩展功能

如需自定义回测参数，可以修改 `simple_batch_backtest.py` 中的 `create_config()` 函数：

```python
config_params = {
    'batch_size': 100,  # 增加批次大小
    'region': 'CHN',    # 改为中国市场
    'universe': 'TOP1000',  # 改为TOP1000股票池
    # ... 其他参数
}
```
