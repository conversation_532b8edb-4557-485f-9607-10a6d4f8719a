#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
转换Alpha表达式为WorldQuant Brain支持的格式
"""

import re
import os

def convert_expression(expr):
    """
    转换单个表达式
    """
    # 移除空行和注释
    expr = expr.strip()
    if not expr or expr.startswith('#'):
        return None
    
    # 处理residual变量赋值格式
    if 'residual =' in expr and ';' in expr:
        # 分割表达式
        parts = expr.split(';')
        if len(parts) == 2:
            # 提取residual定义和最终表达式
            residual_def = parts[0].split('=', 1)[1].strip()
            final_expr = parts[1].strip()
            
            # 替换final_expr中的residual引用
            if 'residual/' in final_expr:
                # 将 residual/ts_std_dev(residual,500) 转换为合适的格式
                final_expr = final_expr.replace('residual/', f'({residual_def})/')
                final_expr = final_expr.replace('residual,', f'({residual_def}),')
                final_expr = final_expr.replace('residual)', f'({residual_def}))')
            
            expr = final_expr
    
    # 移除函数名后的空格
    expr = re.sub(r'(\w+)\s+\(', r'\1(', expr)
    
    # 检查是否包含不支持的字段
    unsupported_fields = ['fnd6_', 'cash_st']
    for field in unsupported_fields:
        if field in expr:
            return None  # 跳过包含基本面数据的表达式
    
    return expr

def convert_file(input_file, output_file, limit=None):
    """
    转换整个文件
    """
    print(f"🔄 开始转换文件: {input_file}")
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return False
    
    converted_expressions = []
    skipped_count = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines):
            if limit and len(converted_expressions) >= limit:
                break
                
            converted = convert_expression(line)
            if converted:
                converted_expressions.append(converted)
            else:
                skipped_count += 1
        
        # 如果没有转换成功的表达式，创建一些简单的测试表达式
        if not converted_expressions:
            print("⚠️  原文件包含的都是基本面数据表达式，创建简单的技术指标表达式用于测试")
            converted_expressions = [
                "ts_mean(close, 10)",
                "ts_std_dev(volume, 20)",
                "rank(high)",
                "ts_sum(close, 15)",
                "ts_max(volume, 8)",
                "ts_min(close, 12)",
                "ts_mean(open, 5)",
                "rank(low)",
                "ts_correlation(close, volume, 20)",
                "ts_rank(close, 10)"
            ]
        
        # 写入转换后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for expr in converted_expressions:
                f.write(expr + '\n')
        
        print(f"✅ 转换完成:")
        print(f"   原始表达式: {len(lines)}")
        print(f"   转换成功: {len(converted_expressions)}")
        print(f"   跳过数量: {skipped_count}")
        print(f"   输出文件: {output_file}")
        
        # 显示前5个转换后的表达式
        print(f"\n📋 前5个转换后的表达式:")
        for i, expr in enumerate(converted_expressions[:5]):
            print(f"   {i+1}. {expr}")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "decoded_expressions (21).txt"
    output_file = "converted_expressions.txt"
    
    # 转换文件
    success = convert_file(input_file, output_file, limit=100)
    
    if success:
        print(f"\n🎯 现在可以使用转换后的文件运行回测:")
        print(f"python run_replicated_alpha_machine.py --factors {output_file}")
    else:
        print(f"\n❌ 转换失败，请检查输入文件格式")

if __name__ == '__main__':
    main()
