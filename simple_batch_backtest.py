#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版批量回测脚本 - 对decoded_expressions-6.txt中的因子进行批量回测
直接使用jy_worldquant中现有的功能
"""

import os
import sys
import logging
from datetime import datetime

# 添加jy_worldquant目录到Python路径
jy_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'jy_worldquant')
sys.path.insert(0, jy_path)

# 导入现有的功能
from auto_ex_new import batch_simulate, load_task_pool
from common_config import CommonConfig
from common_auth import BrainAuth
from db_utils import DatabaseService
import sqlite3


def setup_logging():
    """设置日志系统"""
    # 创建logs目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 设置日志文件名
    log_file = os.path.join(
        log_dir, 
        f"simple_backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    )
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    return logger


def load_factors_from_file(file_path, decay=6):
    """
    从文件加载因子表达式
    
    Args:
        file_path: 因子文件路径
        decay: 衰减参数
        
    Returns:
        因子列表，每个元素为 (expression, decay) 元组
    """
    factors = []
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"因子文件不存在: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if line and not line.startswith('#'):  # 跳过空行和注释行
                factors.append((line, decay))
    
    return factors


def create_config():
    """创建配置对象"""
    config_params = {
        # 基本参数
        'region': 'USA',
        'universe': 'TOP3000',
        'delay': 0,
        'decay': 6,
        'neutralization': 'SUBINDUSTRY',
        'dataset_id': 'model26',
        'prefix': 'winsorize',
        'events': 'open_events',
        'coverage': 0.6,
        
        # 回测参数
        'truncation': 0.08,
        'instrument_type': 'EQUITY',
        'pasteurization': 'ON',
        'nan_handling': 'ON',
        'unit_handling': 'VERIFY',
        'language': 'FASTEXPR',
        'visualization': 'false',
        'max_trade': 'ON',
        'test_period': 'P0Y',
        
        # 批量处理参数
        'batch_size': 50,  # 每批处理50个因子
        'alpha_num_filter': 2000,
        'alpha_num_submit': 500,
        'third_promote_num': 200,
        'second_promote_sharp': 1.2,
        'second_promote_fitness': 0.7,
        'third_promote_sharp': 1.3,
        'third_promote_fitness': 0.8,
        'submit_sharp_th': 1.58,
        'submit_fitness_th': 1,
        'submit_margin_th': 1,
        
        # 流程控制参数
        'is_simulate': 'true',
        'is_second_promote': 'false',  # 暂时关闭二阶提升
        'is_third_promote': 'false',   # 暂时关闭三阶提升
        'is_prune': 'false',           # 暂时关闭剪枝
        'is_filter_pnl': 'false',
        'is_preprocess_alpha': 'true',
        
        # 数据库参数
        'db_file': 'data/backtest_results.db',
        'enabled': 'true',
        'store_all_alphas': 'true',
        'timeout': 10
    }
    
    return CommonConfig(**config_params)


def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()

    try:
        # 因子文件路径
        factors_file = "decoded_expressions-6.txt"

        logger.info("=== 开始批量回测 ===")
        logger.info(f"因子文件: {factors_file}")

        # 1. 检查文件是否存在
        if not os.path.exists(factors_file):
            logger.error(f"因子文件不存在: {factors_file}")
            return

        # 2. 创建配置
        logger.info("创建配置对象")
        config = create_config()

        # 3. 切换到jy_worldquant目录进行登录
        logger.info("验证WorldQuant BRAIN登录")
        original_dir = os.getcwd()
        try:
            os.chdir(jy_path)  # 切换到jy_worldquant目录
            auth = BrainAuth()
            session = auth.login()
            if not session:
                logger.error("登录失败，请检查凭证文件 jy_worldquant/brain_credentials.txt")
                return
            logger.info("登录成功")
        finally:
            os.chdir(original_dir)  # 切换回原目录
        
        # 4. 设置数据库
        logger.info("初始化数据库")
        # 创建数据库目录
        db_file = "data/backtest_results.db"
        os.makedirs(os.path.dirname(db_file), exist_ok=True)

        # 创建数据库连接
        db_conn = sqlite3.connect(db_file)
        logger.info(f"数据库连接创建成功: {db_file}")
        
        # 5. 加载因子
        logger.info("加载因子表达式")
        factors = load_factors_from_file(factors_file, config.decay)
        logger.info(f"成功加载 {len(factors)} 个因子表达式")
        
        if not factors:
            logger.warning("没有找到任何因子，退出")
            return
        
        # 6. 开始批量回测
        logger.info(f"开始批量回测，批次大小: {config.batch_size}")
        
        total_batches = len(factors) // config.batch_size + (1 if len(factors) % config.batch_size else 0)
        logger.info(f"总共需要处理 {total_batches} 个批次")
        
        # 使用现有的batch_simulate函数
        progress_count = 0
        for progress in batch_simulate(factors, config, db_conn):
            progress_count += 1
            logger.info(f"批次 {progress_count} 完成，总进度: {progress}%")
        
        logger.info("=== 批量回测完成！===")
        
    except Exception as e:
        logger.error(f"批量回测失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
