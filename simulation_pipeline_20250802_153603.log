INFO - [32m初始化数据库连接[0m
INFO - [32m数据库连接成功[0m
INFO - [32m数据库表创建/检查完成[0m
INFO - [32mcheck_status 字段已存在，跳过迁移[0m
INFO - [32m数据库初始化完成[0m
INFO - [32m创建异步数据库服务[0m
INFO - [32m异步数据库服务已初始化，线程池大小: 3[0m
INFO - [32m✅ 服务初始化成功[0m
INFO - [32m📧 认证服务: BrainAuth[0m
INFO - [32m💾 数据库服务: AsyncDatabaseService[0m
INFO - [32m🔬 仿真服务: SimulationService[0m
INFO - [32m⚡ 速率限制: 最大并发2, 检查间隔15秒[0m
INFO - [32m📝 成功Alpha记录文件已初始化: successful_alphas_20250802_153603.csv[0m
INFO - [32m🚀 优化仿真流水线系统初始化完成[0m
INFO - [32m📁 目标文件: filtered_shuffled_expressions.txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m⚙️ 配置: 8槽位, 批次大小10[0m
INFO - [32m🔄 断点续传: 从第1个任务开始[0m
INFO - [32m🚀 启动优化仿真流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: filtered_shuffled_expressions.txt[0m
INFO - [32m✅ 成功加载 65550 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: rank(divide(ts_rank(mdl77_pctchg3yfcf, 100), ts_st...[0m
INFO - [32m  2. 2: rank(divide(ts_rank(oth455_customer_n2v_p10_q200_w...[0m
INFO - [32m  3. 3: rank(divide(ts_rank(anl15_gr_cal_fy2_ests, 10), ts...[0m
INFO - [32m  4. 4: rank(divide(ts_rank(eps, 60), ts_std_dev(eps, 100)...[0m
INFO - [32m  5. 5: rank(divide(ts_rank(oth432_sgpp_trkdpitdeltapredic...[0m
INFO - [32m🔍 过滤已回测的Alpha...[0m
INFO - [32m已过滤掉 0 个已回测的alpha，剩余 65550 个新alpha待回测[0m
INFO - [32m✅ 过滤后剩余 65550 个新Alpha[0m
INFO - [32m📦 创建仿真任务，每批10个Alpha[0m
INFO - [32m🔄 断点续传: 跳过前10个Alpha，剩余65540个[0m
INFO - [32m✅ 创建6554个任务 (编号2-6555)[0m
INFO - [32m🚀 开始启动 8 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 启动[0m
INFO - [32m🔧 槽位2 启动[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位4 启动[0m
INFO - [32m🔧 槽位5 启动[0m
INFO - [32m🔧 槽位6 启动[0m
INFO - [32m🔧 槽位7 启动[0m
INFO - [32m🔬 [槽位1] 开始处理任务2，包含10个Alpha[0m
INFO - [32m开始仿真 10 个Alpha[0m
INFO - [32m准备仿真 1 个任务[0m
INFO - [32m🔬 [槽位7] 开始处理任务3，包含10个Alpha[0m
INFO - [32m🔬 [槽位3] 开始处理任务4，包含10个Alpha[0m
INFO - [32m🔬 [槽位2] 开始处理任务5，包含10个Alpha[0m
INFO - [32m登录成功[0m
INFO - [32m响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}[0m
INFO - [32m任务 0 提交成功[0m
INFO - [32m🔬 [槽位0] 开始处理任务6，包含10个Alpha[0m
INFO - [32m🔬 [槽位5] 开始处理任务7，包含10个Alpha[0m
INFO - [32m🔬 [槽位4] 开始处理任务8，包含10个Alpha[0m
INFO - [32m🔬 [槽位6] 开始处理任务9，包含10个Alpha[0m
INFO - [32m============================================================[0m
INFO - [32m📊 实时统计[0m
INFO - [32m============================================================[0m
INFO - [32m📋 总任务: 6554[0m
INFO - [32m⏳ 待处理: 6546[0m
INFO - [32m🔄 运行中: 1[0m
INFO - [32m✅ 已完成: 0[0m
INFO - [32m❌ 失败: 0[0m
INFO - [32m🎯 总Alpha: 65540[0m
INFO - [32m🆔 成功Alpha: 0[0m
INFO - [32m📈 完成率: 0.0%[0m
INFO - [32m📈 成功率: 0.0%[0m
INFO - [32m⚡ 效率: 0.0 Alpha/分钟[0m
INFO - [32m🔧 活跃槽位: 8/8[0m
INFO - [32m============================================================[0m
INFO - [32m开始仿真 10 个Alpha[0m
INFO - [32m开始仿真 10 个Alpha[0m
INFO - [32m开始仿真 10 个Alpha[0m
INFO - [32m准备仿真 1 个任务[0m
INFO - [32m开始仿真 10 个Alpha[0m
INFO - [32m准备仿真 1 个任务[0m
INFO - [32m开始仿真 10 个Alpha[0m
INFO - [32m准备仿真 1 个任务[0m
INFO - [32m开始仿真 10 个Alpha[0m
INFO - [32m开始仿真 10 个Alpha[0m
INFO - [32m准备仿真 1 个任务[0m
INFO - [32m准备仿真 1 个任务[0m
INFO - [32m准备仿真 1 个任务[0m
INFO - [32m准备仿真 1 个任务[0m
INFO - [32m任务 0 提交成功[0m
INFO - [32m任务 0 提交成功[0m
INFO - [32m任务 0 提交成功[0m
INFO - [32m任务 0 提交成功[0m
INFO - [32m任务 0 提交成功[0m
INFO - [32m任务 0 提交成功[0m
INFO - [32m任务 0 提交成功[0m
ERROR - [31m请求失败: GET https://api.worldquantbrain.com/simulations/3jBkfycHT5hG9Ro1dwyY2w6l, 错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))[0m
ERROR - [31m任务 0 状态检查失败：无响应[0m
ERROR - [31m请求失败: GET https://api.worldquantbrain.com/simulations/2N33VecL055rchmRAvyFKZZ, 错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))[0m
ERROR - [31m请求失败: GET https://api.worldquantbrain.com/simulations/38j1N5E14H7cjy1htM4lHAB, 错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))[0m
ERROR - [31m任务 0 状态检查失败：无响应[0m
ERROR - [31m任务 0 状态检查失败：无响应[0m
