#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化版本的流水线主程序
使用jy_worldquant项目的SimulationService进行回测
"""

# 导入asyncio库，用于运行异步主函数
import asyncio
# 从核心逻辑文件中导入主流水线类
from pipeline_core_v2 import OptimizedSimulationPipeline
# 从配置文件中导入默认因子文件名
from pipeline_config_and_data_structures_v2 import DEFAULT_FACTORS_FILE

async def main():
    """主程序"""
    print("🚀 优化仿真流水线回测系统 (SimulationService版本)")
    print("=" * 80)
    print(f"📁 目标文件: {DEFAULT_FACTORS_FILE}")
    print("🔧 配置: 使用jy_worldquant项目的SimulationService")
    print("📊 特性: 断点续传 + 成功Alpha自动记录 + 数据库过滤")
    print("=" * 80)

    # 🔍 自动检测并处理断点续传
    suggested_resume, progress_info = OptimizedSimulationPipeline.detect_resume_point()
    resume_from = 0

    if suggested_resume > 0 and progress_info:
        print(f"\n🔍 检测到上次运行进度:")
        print(f"   📁 文件: {progress_info.get('factors_file', '未知')}")
        print(f"   📊 进度: {progress_info.get('completed_tasks', 0)}/{progress_info.get('total_tasks', 0)} 任务 ({progress_info.get('progress_percentage', 0):.1f}%)")
        print(f"   ✅ 成功: {progress_info.get('successful_alphas', 0)} Alpha")
        print(f"   ❌ 失败: {progress_info.get('failed_tasks', 0)} 任务")
        print(f"   🔄 剩余: {progress_info.get('remaining_tasks', 0)} 任务")
        print(f"   🛡️ 断点检测: {progress_info.get('current_vs_max', 'N/A')}")
        print(f"   💡 建议从第 {suggested_resume} 个任务继续 (防止断点回退)")

        resume_choice = input(f"\n是否从第{suggested_resume}个任务继续？(Y/n/手动输入): ").strip().lower()

        if resume_choice in ['', 'y', 'yes']:
            resume_from = suggested_resume
            print(f"✅ 将从第{resume_from}个任务继续执行")
        elif resume_choice in ['n', 'no']:
            print("✅ 将从头开始执行")
            resume_from = 0
        else:
            # 尝试将输入转换为整数作为手动续传点
            try:
                manual_resume = int(resume_choice)
                resume_from = manual_resume
                print(f"✅ 将从第{resume_from}个任务开始执行")
            except ValueError:
                try:
                    manual_resume = int(input("请输入续传的任务序号: "))
                    resume_from = manual_resume
                    print(f"✅ 将从第{resume_from}个任务开始执行")
                except ValueError:
                    print("❌ 输入无效，将从头开始")
                    resume_from = 0
    else:
        print(f"\n💡 没有检测到有效的进度文件")
        resume_choice = input("是否需要手动设置断点续传？(y/N): ").strip().lower()
        if resume_choice == 'y':
            try:
                resume_from = int(input("请输入续传的任务序号 (从第几个任务开始): "))
                print(f"✅ 将从第{resume_from}个任务开始执行")
            except ValueError:
                print("❌ 输入无效，将从头开始")
                resume_from = 0

    # 获取用户输入来配置仿真参数
    print("\n🔧 配置仿真参数:")

    # 配置并发槽位数
    slots_choice = input("并发槽位数 (建议1-4，回车默认4): ").strip()
    try:
        max_slots = int(slots_choice) if slots_choice else 4
        max_slots = max(1, min(max_slots, 8))  # 限制在1到8之间
    except ValueError:
        max_slots = 4
        print(f"⚠️ 输入无效，使用默认值: {max_slots}")

    # 配置批次大小
    batch_choice = input("批次大小 (建议50-200，回车默认100): ").strip()
    try:
        batch_size = int(batch_choice) if batch_choice else 100
        batch_size = max(10, min(batch_size, 500))  # 限制在10到500之间
    except ValueError:
        batch_size = 100
        print(f"⚠️ 输入无效，使用默认值: {batch_size}")

    print(f"✅ 配置确认: {max_slots}个槽位, 批次大小{batch_size}")

    # 根据用户配置创建流水线系统实例
    pipeline = OptimizedSimulationPipeline(
        resume_from=resume_from,
        max_slots=max_slots,
        batch_size=batch_size,
        config_file="config_v2.ini"
    )

    # 让用户选择运行模式（测试或完整回测）
    print("\n📊 请选择运行模式:")
    print("1. 测试模式 (30个Alpha)")
    print("2. 小规模测试 (100个Alpha)")
    print("3. 中等规模测试 (500个Alpha)")
    print("4. 完整回测 (所有Alpha)")

    choice = input("\n请输入选择 (1-4): ").strip()

    if choice == "1":
        print("\n🧪 开始测试模式...")
        result = await pipeline.run_pipeline(limit=30)
    elif choice == "2":
        print("\n🧪 开始小规模测试...")
        result = await pipeline.run_pipeline(limit=100)
    elif choice == "3":
        print("\n🧪 开始中等规模测试...")
        result = await pipeline.run_pipeline(limit=500)
    elif choice == "4":
        confirm = input("\n⚠️ 完整回测将处理所有Alpha，确认继续？ (y/N): ").strip().lower()
        if confirm == 'y':
            print("\n🚀 开始完整回测...")
            result = await pipeline.run_pipeline()
        else:
            print("❌ 用户取消")
            return
    else:
        print("❌ 无效选择")
        return

    if result and result.get('status') == 'completed':
        print(f"\n🎉 回测完成！")
        print(f"📊 成功: {result['successful_alphas']}/{result['total_alphas']} Alpha")
        print(f"📈 成功率: {result['success_rate']:.1f}%")
        print(f"⚡ 效率: {result['alphas_per_minute']:.1f} Alpha/分钟")
        print(f"⏱️ 耗时: {result['duration_seconds']/60:.2f} 分钟")
        print(f"📝 成功Alpha记录: {result['successful_alphas_file']}")
        print(f"💾 进度文件: {result['progress_file']}")
    else:
        print(f"\n❌ 回测未完成")
        if result and 'error' in result:
            print(f"错误信息: {result['error']}")


if __name__ == "__main__":
    asyncio.run(main())