INFO - [32m初始化数据库连接[0m
INFO - [32m数据库连接成功[0m
INFO - [32m数据库表创建/检查完成[0m
INFO - [32mcheck_status 字段已存在，跳过迁移[0m
INFO - [32m数据库初始化完成[0m
INFO - [32m创建异步数据库服务[0m
INFO - [32m异步数据库服务已初始化，线程池大小: 3[0m
INFO - [32m✅ 服务初始化成功[0m
INFO - [32m📧 认证服务: BrainAuth[0m
INFO - [32m💾 数据库服务: AsyncDatabaseService[0m
INFO - [32m🔬 仿真服务: SimulationService[0m
INFO - [32m📝 成功Alpha记录文件已初始化: successful_alphas_20250802_142849.csv[0m
INFO - [32m🚀 优化仿真流水线系统初始化完成[0m
INFO - [32m📁 目标文件: filtered_shuffled_expressions.txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m⚙️ 配置: 8槽位, 批次大小10[0m
INFO - [32m🔄 断点续传: 从第100个任务开始[0m
INFO - [32m🚀 启动优化仿真流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: filtered_shuffled_expressions.txt[0m
INFO - [32m✅ 成功加载 65550 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: rank(divide(ts_rank(mdl77_pctchg3yfcf, 100), ts_st...[0m
INFO - [32m  2. 2: rank(divide(ts_rank(oth455_customer_n2v_p10_q200_w...[0m
INFO - [32m  3. 3: rank(divide(ts_rank(anl15_gr_cal_fy2_ests, 10), ts...[0m
INFO - [32m  4. 4: rank(divide(ts_rank(eps, 60), ts_std_dev(eps, 100)...[0m
INFO - [32m  5. 5: rank(divide(ts_rank(oth432_sgpp_trkdpitdeltapredic...[0m
INFO - [32m🔍 过滤已回测的Alpha...[0m
INFO - [32m已过滤掉 0 个已回测的alpha，剩余 65550 个新alpha待回测[0m
