#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
运行完全复现的jy_worldquant AlphaMachine
"""

import sys
import argparse
import logging
from pipeline_core_v2 import ReplicatedAlphaMachine


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行复现的Alpha机器')
    parser.add_argument('--config', default='config_v2.ini', help='配置文件路径')
    parser.add_argument('--factors', default='new_test_factors.txt', help='因子文件路径')
    parser.add_argument('--limit', type=int, help='限制处理的Alpha数量')
    
    args = parser.parse_args()
    
    # 创建Alpha机器实例
    alpha_machine = ReplicatedAlphaMachine(
        config_file=args.config,
        factors_file=args.factors
    )
    
    # 运行Alpha机器
    success = alpha_machine.run()
    
    if success:
        print("✅ Alpha机器运行成功完成")
        return 0
    else:
        print("❌ Alpha机器运行失败")
        return 1


if __name__ == '__main__':
    sys.exit(main())
