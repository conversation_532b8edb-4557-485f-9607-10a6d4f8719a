# 第3批次：财务杠杆与偿债能力Alpha (201-300)
# 基于经济学理论：财务杠杆指标与偿债能力指标的回归残差分析

# Alpha 201-210: 净债务与EBITDA偿债能力分析
# 经济学逻辑：净债务/EBITDA比率是核心偿债能力指标，反映企业债务负担和偿债能力
residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_low_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_mean,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_median,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_high,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_low,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_std,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 211-220: 利息覆盖能力分析
# 经济学逻辑：EBIT/利息费用比率反映利息覆盖能力，是评估财务风险的重要指标
residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_mean,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_median,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_high,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_low,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_median,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_high,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_low,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebit_estimate_std,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_ebitda_estimate_std,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 221-230: 现金流偿债能力分析
# 经济学逻辑：现金流与债务的关系反映实际偿债能力，比会计利润更可靠
residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_median,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_high,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_low,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_median,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_high,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_low,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_std,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_std,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 231-240: 现金流与利息费用关系
# 经济学逻辑：现金流覆盖利息费用的能力反映短期偿债压力
residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_mean,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_median,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_high,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_low,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_mean,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_median,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_high,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_low,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_cash_from_operations_estimate_std,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_free_cash_flow_estimate_std,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 241-250: 债务期限结构分析
# 经济学逻辑：不同期间的债务预期变化反映债务管理策略和再融资风险
residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp2,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp3,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp4,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp5,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp2,500), ts_zscore(anl14_median_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp3,500), ts_zscore(anl14_median_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp4,500), ts_zscore(anl14_median_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp5,500), ts_zscore(anl14_median_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp2,500), ts_zscore(anl14_high_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_low_ndebt_fp2,500), ts_zscore(anl14_low_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

# Alpha 251-260: 杠杆与盈利能力关系
# 经济学逻辑：财务杠杆对ROE的放大效应，债务水平与盈利能力的权衡关系
residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp1,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp2,500), ts_zscore(anl14_mean_roe_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp3,500), ts_zscore(anl14_mean_roe_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp1,500), ts_zscore(anl14_median_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp2,500), ts_zscore(anl14_median_roe_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp3,500), ts_zscore(anl14_median_roe_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_mean,500), ts_zscore(anl14_mean_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_median,500), ts_zscore(anl14_median_roe_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp1,500), ts_zscore(anl14_mean_roa_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp1,500), ts_zscore(anl14_median_roa_fp1,500),500); residual/ts_std_dev(residual,500)

# Alpha 261-270: 债务与资产质量关系
# 经济学逻辑：债务水平与资产回报率的关系反映资产使用效率和财务风险
residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_mean,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_median,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_high,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_low,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_mean_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_median_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_low_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_net_debt_estimate_std,500), ts_zscore(pv87_ann_matrix_book_value_share_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 271-280: 折旧摊销与债务关系
# 经济学逻辑：折旧摊销反映资产老化程度，与债务的关系体现资本密集度和投资策略
residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_mean,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_median,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_high,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_low,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_std,500), ts_zscore(pv87_ann_matrix_net_debt_estimate_std,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_mean,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_median,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_median,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_high,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_high,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_low,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_low,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(pv87_ann_matrix_depreciation_amortization_estimate_std,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_std,500),500); residual/ts_std_dev(residual,500)

# Alpha 281-290: 债务预期一致性分析
# 经济学逻辑：分析师对债务预期的一致性反映市场对企业财务风险的共识
residual = ts_regression(ts_zscore(anl14_stddev_ndebt_fp1,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_ndebt_fp2,500), ts_zscore(anl14_mean_ndebt_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_ndebt_fp3,500), ts_zscore(anl14_mean_ndebt_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_ndebt_fp4,500), ts_zscore(anl14_mean_ndebt_fp4,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_stddev_ndebt_fp5,500), ts_zscore(anl14_mean_ndebt_fp5,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp1,500), ts_zscore(anl14_low_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp2,500), ts_zscore(anl14_low_ndebt_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp3,500), ts_zscore(anl14_low_ndebt_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp4,500), ts_zscore(anl14_low_ndebt_fp4,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_high_ndebt_fp5,500), ts_zscore(anl14_low_ndebt_fp5,500),500); residual/ts_std_dev(residual,500)

# Alpha 291-300: 债务分析师覆盖度分析
# 经济学逻辑：债务相关指标的分析师关注度反映市场对财务风险的重视程度
residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp1,500), ts_zscore(anl14_mean_ndebt_fp1,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp2,500), ts_zscore(anl14_mean_ndebt_fp2,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp3,500), ts_zscore(anl14_mean_ndebt_fp3,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp4,500), ts_zscore(anl14_mean_ndebt_fp4,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp5,500), ts_zscore(anl14_mean_ndebt_fp5,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp2,500), ts_zscore(pv87_ann_matrix_interest_expense_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp1,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp2,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)

residual = ts_regression(ts_zscore(anl14_numofests_ndebt_fp3,500), ts_zscore(pv87_ann_matrix_ebitda_estimate_mean,500),500); residual/ts_std_dev(residual,500)
