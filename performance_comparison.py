#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能对比测试脚本
比较原版本和优化版本的功能性和性能
"""

import sys
import logging
import time
import asyncio
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('performance_test')

def test_original_version():
    """测试原版本的基本功能"""
    logger.info("🔍 测试原版本功能...")

    try:
        # 导入原版本模块
        from pipeline_config_and_data_structures import load_alpha_factors
        from pipeline_core import SimulationPipeline

        # 测试因子加载
        alpha_list = load_alpha_factors("test_factors.txt", logger, limit=5)
        logger.info(f"✅ 原版本因子加载成功: {len(alpha_list)}个因子")

        # 测试流水线初始化
        pipeline = SimulationPipeline(
            factors_file="test_factors.txt",
            max_slots=2,
            batch_size=50
        )
        logger.info("✅ 原版本流水线初始化成功")

        return True

    except Exception as e:
        logger.error(f"❌ 原版本测试失败: {e}")
        return False

def test_optimized_version():
    """测试优化版本的基本功能"""
    logger.info("🔍 测试优化版本功能...")

    try:
        # 导入优化版本模块
        from pipeline_config_and_data_structures_v2 import load_alpha_factors, create_simulation_config
        from pipeline_core_v2 import OptimizedSimulationPipeline
        from jy_worldquant.core.simulation_service import SimulationService

        # 测试配置创建
        config, auth_service, db_service = create_simulation_config('config_v2.ini')
        logger.info("✅ 优化版本配置创建成功")

        # 测试SimulationService创建
        simulation_service = SimulationService(config, auth_service, db_service)
        logger.info(f"✅ SimulationService创建成功: {simulation_service.get_service_name()}")

        # 测试因子加载
        alpha_list = load_alpha_factors("test_factors.txt", logger, limit=5)
        logger.info(f"✅ 优化版本因子加载成功: {len(alpha_list)}个因子")

        # 测试流水线初始化
        pipeline = OptimizedSimulationPipeline(
            factors_file="test_factors.txt",
            max_slots=2,
            batch_size=50,
            config_file='config_v2.ini'
        )
        logger.info("✅ 优化版本流水线初始化成功")

        return True

    except Exception as e:
        logger.error(f"❌ 优化版本测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_optimized_simulation():
    """测试优化版本的实际仿真功能"""
    logger.info("🧪 测试优化版本实际仿真...")

    try:
        from pipeline_core_v2 import OptimizedSimulationPipeline

        pipeline = OptimizedSimulationPipeline(
            factors_file="test_factors.txt",
            max_slots=1,
            batch_size=50,
            config_file='config_v2.ini'
        )

        start_time = time.time()
        result = await pipeline.run_pipeline(limit=3)
        end_time = time.time()

        if result and result.get('status') == 'completed':
            logger.info("✅ 优化版本仿真测试成功")
            logger.info(f"   成功Alpha: {result['successful_alphas']}/{result['total_alphas']}")
            logger.info(f"   成功率: {result['success_rate']:.1f}%")
            logger.info(f"   耗时: {end_time - start_time:.2f}秒")
            return True
        else:
            logger.error("❌ 优化版本仿真测试失败")
            return False

    except Exception as e:
        logger.error(f"❌ 优化版本仿真测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_features():
    """比较两个版本的功能特性"""
    logger.info("📊 功能特性对比:")

    features = {
        "断点续传": "✅ 两个版本都支持",
        "并发处理": "✅ 两个版本都支持",
        "进度保存": "✅ 两个版本都支持",
        "成功Alpha记录": "✅ 两个版本都支持",
        "数据库过滤": "🆕 优化版本新增",
        "SimulationService集成": "🆕 优化版本新增",
        "认证管理": "🆕 优化版本改进",
        "配置管理": "🆕 优化版本改进",
        "异步数据库": "🆕 优化版本新增"
    }

    for feature, status in features.items():
        logger.info(f"   {feature}: {status}")

def main():
    """主测试函数"""
    logger.info("🚀 开始性能对比测试")
    logger.info("=" * 80)

    # 测试原版本
    original_success = test_original_version()

    print("\n" + "=" * 80)

    # 测试优化版本
    optimized_success = test_optimized_version()

    print("\n" + "=" * 80)

    # 功能特性对比
    compare_features()

    print("\n" + "=" * 80)

    # 如果基础测试都通过，进行实际仿真测试
    if optimized_success:
        choice = input("基础测试通过！是否进行实际仿真测试？(y/N): ").strip().lower()
        if choice == 'y':
            print("\n" + "=" * 80)
            simulation_success = asyncio.run(test_optimized_simulation())

            if simulation_success:
                logger.info("🎉 所有测试通过！优化版本功能正常")
            else:
                logger.error("❌ 仿真测试失败")
        else:
            logger.info("跳过仿真测试")

    # 总结
    print("\n" + "=" * 80)
    logger.info("📋 测试总结:")
    logger.info(f"   原版本基础功能: {'✅ 通过' if original_success else '❌ 失败'}")
    logger.info(f"   优化版本基础功能: {'✅ 通过' if optimized_success else '❌ 失败'}")

    if original_success and optimized_success:
        logger.info("🎯 结论: 优化版本成功集成SimulationService，保持了原有功能并增加了新特性")
    else:
        logger.error("⚠️ 结论: 存在功能问题，需要进一步调试")

if __name__ == "__main__":
    main()