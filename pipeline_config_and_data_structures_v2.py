#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完全复现jy_worldquant项目的流水线配置和数据结构
使用原项目的AlphaMachine架构
"""

import os
import sys
import logging
import configparser
from datetime import datetime, timedelta
from typing import Optional, List, Tuple, Any

# 添加jy_worldquant项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'jy_worldquant'))

# 导入jy_worldquant项目的核心模块
from jy_worldquant.core import AlphaService, SimulationService, SubmissionService
from jy_worldquant.common_config import CommonConfig
from jy_worldquant.common_auth import BrainAuth
from jy_worldquant.db_utils import setup_database

# 默认的因子文件名
DEFAULT_FACTORS_FILE = "decoded_expressions (21).txt"


def load_alpha_factors(file_path: str, logger: logging.Logger, limit: Optional[int] = None) -> List[Tuple[str, str]]:
    """
    从文件加载Alpha因子

    Args:
        file_path: 文件路径
        logger: 日志记录器
        limit: 限制加载的数量

    Returns:
        (factor_id, expression) 元组列表
    """
    logger.info(f"📁 开始加载因子文件: {file_path}")

    if not os.path.exists(file_path):
        logger.error(f"❌ 文件不存在: {file_path}")
        return []

    alpha_list = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        for i, line in enumerate(lines):
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            # 支持多种格式
            if '\t' in line:
                # TSV格式: factor_id\texpression
                parts = line.split('\t', 1)
                if len(parts) >= 2:
                    factor_id, expression = parts[0], parts[1]
                else:
                    factor_id, expression = str(i+1), parts[0]
            elif ',' in line:
                # CSV格式: factor_id,expression
                parts = line.split(',', 1)
                if len(parts) >= 2:
                    factor_id, expression = parts[0], parts[1]
                else:
                    factor_id, expression = str(i+1), parts[0]
            else:
                # 单列格式: expression
                factor_id, expression = str(i+1), line

            alpha_list.append((factor_id, expression))

            if limit and len(alpha_list) >= limit:
                break

        logger.info(f"✅ 成功加载 {len(alpha_list)} 个Alpha因子 (txt单列模式)")

        # 显示前5个因子示例
        if alpha_list:
            logger.info("📋 前5个因子示例:")
            for i, (fid, expr) in enumerate(alpha_list[:5]):
                logger.info(f"   {i+1}. {fid}: {expr}")

        return alpha_list

    except Exception as e:
        logger.error(f"❌ 加载因子文件失败: {e}")
        return []