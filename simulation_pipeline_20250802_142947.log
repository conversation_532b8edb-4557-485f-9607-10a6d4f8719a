INFO - [32m初始化数据库连接[0m
INFO - [32m数据库连接成功[0m
INFO - [32m数据库表创建/检查完成[0m
INFO - [32mcheck_status 字段已存在，跳过迁移[0m
INFO - [32m数据库初始化完成[0m
INFO - [32m创建异步数据库服务[0m
INFO - [32m异步数据库服务已初始化，线程池大小: 3[0m
INFO - [32m✅ 服务初始化成功[0m
INFO - [32m📧 认证服务: BrainAuth[0m
INFO - [32m💾 数据库服务: AsyncDatabaseService[0m
INFO - [32m🔬 仿真服务: SimulationService[0m
INFO - [32m📝 成功Alpha记录文件已初始化: successful_alphas_20250802_142947.csv[0m
INFO - [32m🚀 优化仿真流水线系统初始化完成[0m
INFO - [32m📁 目标文件: test_factors.txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m⚙️ 配置: 8槽位, 批次大小100[0m
INFO - [32m🚀 启动优化仿真流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: test_factors.txt[0m
INFO - [32m✅ 成功加载 5 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: rank(close)[0m
INFO - [32m  2. 2: ts_mean(close, 20)[0m
INFO - [32m  3. 3: ts_std_dev(volume, 10)[0m
INFO - [32m  4. 4: rank(returns)[0m
INFO - [32m  5. 5: ts_corr(close, volume, 15)[0m
INFO - [32m🔍 过滤已回测的Alpha...[0m
INFO - [32m已过滤掉 2 个已回测的alpha，剩余 3 个新alpha待回测[0m
INFO - [32m✅ 过滤后剩余 3 个新Alpha[0m
INFO - [32m📦 创建仿真任务，每批100个Alpha[0m
INFO - [32m✅ 创建1个任务 (编号1-1)[0m
INFO - [32m🚀 开始启动 8 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 启动[0m
INFO - [32m🔧 槽位2 启动[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位4 启动[0m
INFO - [32m🔧 槽位5 启动[0m
INFO - [32m🔧 槽位6 启动[0m
INFO - [32m🔧 槽位7 启动[0m
INFO - [32m🔬 [槽位1] 开始处理任务1，包含3个Alpha[0m
INFO - [32m开始仿真 3 个Alpha[0m
INFO - [32m准备仿真 1 个任务[0m
INFO - [32m登录成功[0m
INFO - [32m响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}[0m
INFO - [32m任务 0 提交成功[0m
INFO - [32m============================================================[0m
INFO - [32m📊 实时统计[0m
INFO - [32m============================================================[0m
INFO - [32m📋 总任务: 1[0m
INFO - [32m⏳ 待处理: 0[0m
INFO - [32m🔄 运行中: 1[0m
INFO - [32m✅ 已完成: 0[0m
INFO - [32m❌ 失败: 0[0m
INFO - [32m🎯 总Alpha: 3[0m
INFO - [32m🆔 成功Alpha: 0[0m
INFO - [32m📈 完成率: 0.0%[0m
INFO - [32m📈 成功率: 0.0%[0m
INFO - [32m⚡ 效率: 0.0 Alpha/分钟[0m
INFO - [32m🔧 活跃槽位: 1/8[0m
INFO - [32m============================================================[0m
INFO - [32m✅ 任务 0 完成，耗时 127s[0m
INFO - [32m所有仿真任务完成，成功完成 3 个Alpha[0m
INFO - [32m✅ [槽位1] 任务1完成: 3/3 Alpha成功[0m
INFO - [32m============================================================[0m
INFO - [32m📊 实时统计[0m
INFO - [32m============================================================[0m
INFO - [32m📋 总任务: 1[0m
INFO - [32m⏳ 待处理: 0[0m
INFO - [32m🔄 运行中: 0[0m
INFO - [32m✅ 已完成: 1[0m
INFO - [32m❌ 失败: 0[0m
INFO - [32m🎯 总Alpha: 3[0m
INFO - [32m🆔 成功Alpha: 3[0m
INFO - [32m📈 完成率: 100.0%[0m
INFO - [32m📈 成功率: 100.0%[0m
INFO - [32m⚡ 效率: 1.4 Alpha/分钟[0m
INFO - [32m🔧 活跃槽位: 0/8[0m
INFO - [32m============================================================[0m
INFO - [32m🏁 槽位7 结束[0m
INFO - [32m🏁 槽位3 结束[0m
INFO - [32m🏁 槽位2 结束[0m
INFO - [32m🏁 槽位0 结束[0m
INFO - [32m🏁 槽位5 结束[0m
INFO - [32m🏁 槽位4 结束[0m
INFO - [32m🏁 槽位6 结束[0m
INFO - [32m🏁 槽位1 结束[0m
INFO - [32m================================================================================[0m
INFO - [32m🎯 优化仿真流水线最终报告[0m
INFO - [32m================================================================================[0m
INFO - [32m📊 总任务数: 1[0m
INFO - [32m✅ 完成任务: 1[0m
INFO - [32m❌ 失败任务: 0[0m
INFO - [32m🎯 总Alpha数: 3[0m
INFO - [32m🆔 成功Alpha: 3[0m
INFO - [32m📈 成功率: 100.0%[0m
INFO - [32m⚡ 平均效率: 1.4 Alpha/分钟[0m
INFO - [32m⏱️ 总耗时: 2.16 分钟[0m
INFO - [32m📊 总进度: 1个任务完成[0m
INFO - [32m📝 成功Alpha记录: successful_alphas_20250802_142947.csv[0m
INFO - [32m💾 进度文件: simulation_progress_20250802_142947.json[0m
INFO - [32m================================================================================[0m
