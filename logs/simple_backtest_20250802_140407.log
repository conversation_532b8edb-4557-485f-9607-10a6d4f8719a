2025-08-02 14:04:07,434 - __main__ - INFO - 日志系统初始化完成，日志文件: logs/simple_backtest_20250802_140407.log
2025-08-02 14:04:07,434 - __main__ - INFO - === 开始批量回测 ===
2025-08-02 14:04:07,434 - __main__ - INFO - 因子文件: decoded_expressions-6.txt
2025-08-02 14:04:07,434 - __main__ - INFO - 创建配置对象
2025-08-02 14:04:07,435 - __main__ - INFO - 验证WorldQuant BRAIN登录
2025-08-02 14:04:09,042 - root - INFO - 登录成功
2025-08-02 14:04:09,042 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-02 14:04:09,042 - __main__ - INFO - 登录成功
2025-08-02 14:04:09,042 - __main__ - INFO - 初始化数据库
2025-08-02 14:04:09,045 - __main__ - INFO - 数据库连接创建成功: data/backtest_results.db
2025-08-02 14:04:09,045 - __main__ - INFO - 加载因子表达式
2025-08-02 14:04:09,046 - __main__ - INFO - 成功加载 642 个因子表达式
2025-08-02 14:04:09,046 - __main__ - INFO - 开始批量回测，批次大小: 50
2025-08-02 14:04:09,047 - __main__ - INFO - 总共需要处理 13 个批次
2025-08-02 14:04:09,047 - root - INFO - 7.1 批量回测 50个alpha
2025-08-02 14:04:09,047 - __main__ - ERROR - 批量回测失败: can only concatenate str (not "bool") to str
2025-08-02 14:04:09,049 - __main__ - ERROR - 详细错误信息: Traceback (most recent call last):
  File "/Users/<USER>/Downloads/新三阶段/simple_batch_backtest.py", line 197, in main
    for progress in batch_simulate(factors, config, db_conn):
  File "/Users/<USER>/Downloads/新三阶段/jy_worldquant/auto_ex_new.py", line 276, in batch_simulate
    alpha_list = filter_new_alpha_list(
                 ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/新三阶段/jy_worldquant/db_utils.py", line 341, in filter_new_alpha_list
    if not is_alpha_in_db(conn, alpha_expr, decay, common_config):
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/新三阶段/jy_worldquant/db_utils.py", line 219, in is_alpha_in_db
    alpha_hash = get_alpha_hash(alpha, decay, common_config)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/新三阶段/jy_worldquant/db_utils.py", line 211, in get_alpha_hash
    combined = alpha + str(decay) + common_config.dataset_id + common_config.region + common_config.universe + str(common_config.delay) + common_config.instrument_type + common_config.unit_handling + common_config.nan_handling + common_config.language + common_config.visualization + str(common_config.max_trade)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
TypeError: can only concatenate str (not "bool") to str

