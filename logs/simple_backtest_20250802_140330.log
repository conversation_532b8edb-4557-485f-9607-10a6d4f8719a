2025-08-02 14:03:30,818 - __main__ - INFO - 日志系统初始化完成，日志文件: logs/simple_backtest_20250802_140330.log
2025-08-02 14:03:30,818 - __main__ - INFO - === 开始批量回测 ===
2025-08-02 14:03:30,818 - __main__ - INFO - 因子文件: decoded_expressions-6.txt
2025-08-02 14:03:30,818 - __main__ - INFO - 创建配置对象
2025-08-02 14:03:30,819 - __main__ - INFO - 验证WorldQuant BRAIN登录
2025-08-02 14:03:32,479 - root - INFO - 登录成功
2025-08-02 14:03:32,479 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-02 14:03:32,480 - __main__ - INFO - 登录成功
2025-08-02 14:03:32,480 - __main__ - INFO - 初始化数据库
2025-08-02 14:03:32,480 - __main__ - ERROR - 批量回测失败: 'CommonConfig' object has no attribute 'getboolean'
2025-08-02 14:03:32,482 - __main__ - ERROR - 详细错误信息: Traceback (most recent call last):
  File "/Users/<USER>/Downloads/新三阶段/simple_batch_backtest.py", line 171, in main
    db_conn = setup_database(config)
              ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/新三阶段/jy_worldquant/db_utils.py", line 120, in setup_database
    if not config.getboolean('database', 'enabled', fallback=True):
           ^^^^^^^^^^^^^^^^^
AttributeError: 'CommonConfig' object has no attribute 'getboolean'

