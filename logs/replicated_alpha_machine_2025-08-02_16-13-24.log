2025-08-02 16:13:24,132 - ReplicatedAlphaMachine - INFO - 日志系统初始化完成，日志文件: logs/replicated_alpha_machine_2025-08-02_16-13-24.log
2025-08-02 16:13:24,132 - ReplicatedAlphaMachine - INFO - 加载配置文件
2025-08-02 16:13:24,132 - root - INFO - dataset_id: model26
2025-08-02 16:13:24,132 - root - INFO - region: USA
2025-08-02 16:13:24,132 - root - INFO - universe: TOP3000
2025-08-02 16:13:24,132 - root - INFO - delay: 1
2025-08-02 16:13:24,132 - root - INFO - decay: 0
2025-08-02 16:13:24,132 - root - INFO - neutralization: SUBINDUSTRY
2025-08-02 16:13:24,132 - root - INFO - prefix: winsorize
2025-08-02 16:13:24,132 - root - INFO - coverage: 0.8
2025-08-02 16:13:24,132 - root - INFO - truncation: 0.08
2025-08-02 16:13:24,133 - root - INFO - instrument_type: EQUITY
2025-08-02 16:13:24,133 - root - INFO - pasteurization: ON
2025-08-02 16:13:24,133 - root - INFO - nan_handling: OFF
2025-08-02 16:13:24,133 - root - INFO - unit_handling: VERIFY
2025-08-02 16:13:24,133 - root - INFO - language: FASTEXPR
2025-08-02 16:13:24,133 - root - INFO - visualization: false
2025-08-02 16:13:24,133 - root - INFO - max_trade: OFF
2025-08-02 16:13:24,133 - root - INFO - test_period: P0Y
2025-08-02 16:13:24,133 - root - INFO - events: open_events
2025-08-02 16:13:24,133 - root - INFO - alpha_num_filter: 2000
2025-08-02 16:13:24,133 - root - INFO - alpha_num_submit: 500
2025-08-02 16:13:24,133 - root - INFO - third_promote_num: 200
2025-08-02 16:13:24,133 - root - INFO - batch_size: 500
2025-08-02 16:13:24,133 - root - INFO - second_promote_sharp: 1.2
2025-08-02 16:13:24,133 - root - INFO - second_promote_fitness: 0.7
2025-08-02 16:13:24,133 - root - INFO - third_promote_sharp: 1.3
2025-08-02 16:13:24,133 - root - INFO - third_promote_fitness: 0.8
2025-08-02 16:13:24,133 - root - INFO - submit_sharp_th: 1.58
2025-08-02 16:13:24,133 - root - INFO - submit_fitness_th: 1.0
2025-08-02 16:13:24,133 - root - INFO - submit_margin_th: 1.0
2025-08-02 16:13:24,133 - root - INFO - is_simulate: True
2025-08-02 16:13:24,133 - root - INFO - is_second_promote: True
2025-08-02 16:13:24,133 - root - INFO - is_third_promote: True
2025-08-02 16:13:24,133 - root - INFO - is_prune: False
2025-08-02 16:13:24,133 - root - INFO - fields_file: filtered_shuffled_expressions.txt
2025-08-02 16:13:24,133 - root - INFO - is_filter_pnl: False
2025-08-02 16:13:24,133 - root - INFO - is_preprocess_alpha: True
2025-08-02 16:13:24,133 - ReplicatedAlphaMachine - INFO - 初始化数据库服务
2025-08-02 16:13:24,133 - root - INFO - 初始化数据库连接
2025-08-02 16:13:24,133 - root - INFO - 数据库连接成功
2025-08-02 16:13:24,134 - root - INFO - 数据库表创建/检查完成
2025-08-02 16:13:24,134 - root - INFO - check_status 字段已存在，跳过迁移
2025-08-02 16:13:24,134 - root - INFO - 数据库初始化完成
2025-08-02 16:13:24,134 - root - INFO - 创建异步数据库服务
2025-08-02 16:13:24,134 - AsyncDatabaseService - INFO - 异步数据库服务已初始化，线程池大小: 3
2025-08-02 16:13:24,134 - ReplicatedAlphaMachine - INFO - 初始化认证服务
2025-08-02 16:13:24,134 - ReplicatedAlphaMachine - INFO - 初始化业务服务
2025-08-02 16:13:24,134 - ReplicatedAlphaMachine - INFO - Alpha机器初始化完成
2025-08-02 16:13:24,134 - ReplicatedAlphaMachine - INFO - 开始Alpha生成和仿真流程
2025-08-02 16:13:24,134 - ReplicatedAlphaMachine - INFO - 开始生成Alpha表达式
2025-08-02 16:13:24,134 - AlphaService - INFO - 从文件 decoded_expressions (21).txt 创建Alpha
2025-08-02 16:13:24,135 - AlphaService - INFO - 从文件加载了 2000 个数据字段
2025-08-02 16:13:24,180 - AlphaFactory - INFO - 生成了 226000 个一阶Alpha表达式
2025-08-02 16:13:24,211 - AlphaService - INFO - 生成了 226000 个Alpha表达式
2025-08-02 16:13:24,214 - ReplicatedAlphaMachine - INFO - 生成了 226000 个Alpha，开始过滤已回测的Alpha
2025-08-02 16:13:25,132 - root - INFO - 已过滤掉 0 个已回测的alpha，剩余 226000 个新alpha待回测
2025-08-02 16:13:25,143 - ReplicatedAlphaMachine - INFO - 过滤后剩余 226000 个未回测的Alpha
2025-08-02 16:13:25,143 - ReplicatedAlphaMachine - INFO - 执行日期范围: 12-01 到 12-07
2025-08-02 16:13:25,143 - ReplicatedAlphaMachine - INFO - 开始批量仿真 226000 个Alpha
2025-08-02 16:13:25,143 - SimulationService - INFO - 开始批量仿真，批次大小: 500
2025-08-02 16:13:25,143 - SimulationService - INFO - 进行第 1/452 批仿真，数量: 500
2025-08-02 16:13:25,143 - SimulationService - INFO - 开始仿真 500 个Alpha
2025-08-02 16:13:25,143 - SimulationService - INFO - 准备仿真 50 个任务
2025-08-02 16:13:27,377 - root - INFO - 登录成功
2025-08-02 16:13:27,377 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-02 16:13:27,936 - SimulationService - WARNING - 触发速率限制，等待 30 秒
2025-08-02 16:13:59,401 - SimulationService - WARNING - 触发速率限制，等待 30 秒
2025-08-02 16:14:30,902 - SimulationService - WARNING - 触发速率限制，等待 30 秒
2025-08-02 16:15:03,233 - SimulationService - INFO - 任务 0 提交成功
2025-08-02 16:15:03,680 - SimulationService - INFO - 任务 1 提交成功
2025-08-02 16:15:04,223 - SimulationService - INFO - 任务 2 提交成功
2025-08-02 16:15:04,749 - SimulationService - INFO - 任务 3 提交成功
2025-08-02 16:15:05,456 - SimulationService - INFO - 任务 4 提交成功
2025-08-02 16:15:05,927 - SimulationService - INFO - 任务 5 提交成功
2025-08-02 16:15:06,475 - SimulationService - INFO - 任务 6 提交成功
2025-08-02 16:15:07,090 - SimulationService - INFO - 任务 7 提交成功
2025-08-02 16:15:12,929 - SimulationService - ERROR - ❌ 任务 0 失败，包含的Alpha表达式：
2025-08-02 16:15:12,929 - SimulationService - ERROR -    - Alpha: winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), Decay: 0
2025-08-02 16:15:12,929 - SimulationService - ERROR -    - Alpha: reverse(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:15:12,929 - SimulationService - ERROR -    - Alpha: inverse(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:15:12,929 - SimulationService - ERROR -    - Alpha: rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:15:12,929 - SimulationService - ERROR -    - Alpha: zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:15:12,929 - SimulationService - ERROR -    - Alpha: quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:15:12,930 - SimulationService - ERROR -    - Alpha: normalize(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:15:12,930 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, constant=0), Decay: 0
2025-08-02 16:15:12,930 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, constant=0), Decay: 0
2025-08-02 16:15:12,930 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, constant=0), Decay: 0
2025-08-02 16:15:12,930 - SimulationService - WARNING - ❌ 任务 0 失败https://api.worldquantbrain.com/simulations/295ETDfJd4Hc93S2uiXWFua: {"children":["160CqKfIN4IAad3irmdXB8V","2TIqmCeRp4KSbDa17HGsDsaA","2vozKx8Mi4U2ctd1cgB7F2E8","2GrgFGaDH53qa3SmBMANNZu","4czvedcbr4N6cxDNgv61vIa","2SHzR3HU4LubIUiJ4VVrC4","3oCxaadgV593bUe6xTSvCZU","3eXKYx4Ho5jzch2FpdxDkt0","gnt7RfuW4YL9GkaggHUeX1","rHmxraJ35649ETgW7aXBqh"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:15:12,930 - SimulationService - INFO - 正在查询任务 0 的 10 个子任务详情...
2025-08-02 16:15:13,302 - SimulationService - INFO - 📄 任务 0 子任务 1/10 (ID: 160CqKfIN4IAad3irmdXB8V):
2025-08-02 16:15:13,303 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:13,303 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:14,155 - SimulationService - INFO - 📄 任务 0 子任务 2/10 (ID: 2TIqmCeRp4KSbDa17HGsDsaA):
2025-08-02 16:15:14,156 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:14,156 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:15,250 - SimulationService - INFO - 📄 任务 0 子任务 3/10 (ID: 2vozKx8Mi4U2ctd1cgB7F2E8):
2025-08-02 16:15:15,251 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:15,251 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:16,817 - SimulationService - INFO - 📄 任务 0 子任务 4/10 (ID: 2GrgFGaDH53qa3SmBMANNZu):
2025-08-02 16:15:16,818 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:16,818 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:17,739 - SimulationService - INFO - 📄 任务 0 子任务 5/10 (ID: 4czvedcbr4N6cxDNgv61vIa):
2025-08-02 16:15:17,739 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:17,740 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:18,596 - SimulationService - INFO - 📄 任务 0 子任务 6/10 (ID: 2SHzR3HU4LubIUiJ4VVrC4):
2025-08-02 16:15:18,597 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:18,597 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:19,583 - SimulationService - INFO - 📄 任务 0 子任务 7/10 (ID: 3oCxaadgV593bUe6xTSvCZU):
2025-08-02 16:15:19,583 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:19,583 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:20,419 - SimulationService - INFO - 📄 任务 0 子任务 8/10 (ID: 3eXKYx4Ho5jzch2FpdxDkt0):
2025-08-02 16:15:20,420 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:20,420 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:21,323 - SimulationService - INFO - 📄 任务 0 子任务 9/10 (ID: gnt7RfuW4YL9GkaggHUeX1):
2025-08-02 16:15:21,324 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:21,324 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:22,245 - SimulationService - INFO - 📄 任务 0 子任务 10/10 (ID: rHmxraJ35649ETgW7aXBqh):
2025-08-02 16:15:22,245 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:22,245 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:23,251 - SimulationService - INFO - 任务 8 提交成功
2025-08-02 16:15:23,563 - SimulationService - ERROR - ❌ 任务 1 失败，包含的Alpha表达式：
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, constant=0), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, constant=0), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:15:23,564 - SimulationService - WARNING - ❌ 任务 1 失败https://api.worldquantbrain.com/simulations/4tqcAl4SF4YObWMGzevlA8h: {"children":["3euLI51oz54gbFp13gGTrILK","4adpE15GX57cbir10DKEOGzu","1M4Ax4fHW4Jy8JS18YkCI4wL","4Wcf25V94O78VGKX4rV8zC","2SifIgcOS5cN8YNHbIGvPVC","4GvCau1SK4HpaMExh397Z25","46nLZs4vw4BdcCdFeoVzwRQ","1St4kH8Lu5dCaJr3xQrrGdj","1lBkdq6o7581bDi1bMmvwCxN","2qL9qR2Es4C69A0aHrFBJgw"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:15:23,564 - SimulationService - INFO - 正在查询任务 1 的 10 个子任务详情...
2025-08-02 16:15:23,882 - SimulationService - INFO - 📄 任务 1 子任务 1/10 (ID: 3euLI51oz54gbFp13gGTrILK):
2025-08-02 16:15:23,883 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:23,883 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:24,806 - SimulationService - INFO - 📄 任务 1 子任务 2/10 (ID: 4adpE15GX57cbir10DKEOGzu):
2025-08-02 16:15:24,806 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:24,806 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:25,932 - SimulationService - INFO - 📄 任务 1 子任务 3/10 (ID: 1M4Ax4fHW4Jy8JS18YkCI4wL):
2025-08-02 16:15:25,933 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:25,933 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:26,825 - SimulationService - INFO - 📄 任务 1 子任务 4/10 (ID: 4Wcf25V94O78VGKX4rV8zC):
2025-08-02 16:15:26,826 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:26,826 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:27,672 - SimulationService - INFO - 📄 任务 1 子任务 5/10 (ID: 2SifIgcOS5cN8YNHbIGvPVC):
2025-08-02 16:15:27,673 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:27,673 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:28,615 - SimulationService - INFO - 📄 任务 1 子任务 6/10 (ID: 4GvCau1SK4HpaMExh397Z25):
2025-08-02 16:15:28,615 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:28,615 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:29,457 - SimulationService - INFO - 📄 任务 1 子任务 7/10 (ID: 46nLZs4vw4BdcCdFeoVzwRQ):
2025-08-02 16:15:29,457 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:29,457 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:30,353 - SimulationService - INFO - 📄 任务 1 子任务 8/10 (ID: 1St4kH8Lu5dCaJr3xQrrGdj):
2025-08-02 16:15:30,353 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:30,353 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:31,234 - SimulationService - INFO - 📄 任务 1 子任务 9/10 (ID: 1lBkdq6o7581bDi1bMmvwCxN):
2025-08-02 16:15:31,235 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:31,235 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:32,086 - SimulationService - INFO - 📄 任务 1 子任务 10/10 (ID: 2qL9qR2Es4C69A0aHrFBJgw):
2025-08-02 16:15:32,086 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:32,086 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:33,508 - SimulationService - INFO - 任务 9 提交成功
2025-08-02 16:15:33,841 - SimulationService - ERROR - ❌ 任务 2 失败，包含的Alpha表达式：
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:15:33,842 - SimulationService - WARNING - ❌ 任务 2 失败https://api.worldquantbrain.com/simulations/2C7f3r17W4l3cxEMJhgqeA7: {"children":["3LE8TA6XV4TOcxZD84d2Fry","4jXGv6aEp4Hvbg115GAa8wxJ","4zYHCE9Hr4IR9M91gzf6JVOg","3gLdJE9AK4npauppdoNMEjK","27S88tc7V4SpcCaOwwNCbjH","3rnMBj1e4hT8QT8pXDZGuv","32y19E90c4Rvc3VyY8IdJHN","2kAiegcW44kQaEi186q7OgbK","3ksBOWgUz4oAbcfj4tYRwQu","1WRxV37pC58jbuT6uFRvEjC"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:15:33,842 - SimulationService - INFO - 正在查询任务 2 的 10 个子任务详情...
2025-08-02 16:15:34,203 - SimulationService - INFO - 📄 任务 2 子任务 1/10 (ID: 3LE8TA6XV4TOcxZD84d2Fry):
2025-08-02 16:15:34,203 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:34,204 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:35,750 - SimulationService - INFO - 📄 任务 2 子任务 2/10 (ID: 4jXGv6aEp4Hvbg115GAa8wxJ):
2025-08-02 16:15:35,750 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:35,750 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:36,581 - SimulationService - INFO - 📄 任务 2 子任务 3/10 (ID: 4zYHCE9Hr4IR9M91gzf6JVOg):
2025-08-02 16:15:36,582 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:36,582 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:37,404 - SimulationService - INFO - 📄 任务 2 子任务 4/10 (ID: 3gLdJE9AK4npauppdoNMEjK):
2025-08-02 16:15:37,404 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:37,404 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:38,324 - SimulationService - INFO - 📄 任务 2 子任务 5/10 (ID: 27S88tc7V4SpcCaOwwNCbjH):
2025-08-02 16:15:38,324 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:38,324 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:39,165 - SimulationService - INFO - 📄 任务 2 子任务 6/10 (ID: 3rnMBj1e4hT8QT8pXDZGuv):
2025-08-02 16:15:39,165 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:39,165 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:40,231 - SimulationService - INFO - 📄 任务 2 子任务 7/10 (ID: 32y19E90c4Rvc3VyY8IdJHN):
2025-08-02 16:15:40,232 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:40,232 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:41,087 - SimulationService - INFO - 📄 任务 2 子任务 8/10 (ID: 2kAiegcW44kQaEi186q7OgbK):
2025-08-02 16:15:41,087 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:41,088 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:42,008 - SimulationService - INFO - 📄 任务 2 子任务 9/10 (ID: 3ksBOWgUz4oAbcfj4tYRwQu):
2025-08-02 16:15:42,008 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:42,008 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:42,894 - SimulationService - INFO - 📄 任务 2 子任务 10/10 (ID: 1WRxV37pC58jbuT6uFRvEjC):
2025-08-02 16:15:42,894 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:42,894 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:43,863 - SimulationService - INFO - 任务 10 提交成功
2025-08-02 16:15:44,215 - SimulationService - ERROR - ❌ 任务 3 失败，包含的Alpha表达式：
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:15:44,216 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:15:44,217 - SimulationService - WARNING - ❌ 任务 3 失败https://api.worldquantbrain.com/simulations/L3l1S9ix5ceb9RYlemfsO7: {"children":["6zCsH8NJ4jn9DWWIYSnhzA","3UmJ9uetx53DbEuycAbYGUp","2ou02f67o4pmakY1hr8vjGJT","2hvzI99uv4RZ91ikI4HIi8E","unX3Y4kz4wH9VE13CEswOOB","3PM88pdMG4RqbuKFrSZEOhO","2vdJuP25N4sK8LRKCucbLUU","1rf2DZdrp57z9dpUBlsEX8r","2XwFMubBI4LdaSKEkANfr9r","1Cfrj0fBu4ZncoIGvfezXd5"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:15:44,217 - SimulationService - INFO - 正在查询任务 3 的 10 个子任务详情...
2025-08-02 16:15:44,539 - SimulationService - INFO - 📄 任务 3 子任务 1/10 (ID: 6zCsH8NJ4jn9DWWIYSnhzA):
2025-08-02 16:15:44,539 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:44,539 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:45,489 - SimulationService - INFO - 📄 任务 3 子任务 2/10 (ID: 3UmJ9uetx53DbEuycAbYGUp):
2025-08-02 16:15:45,490 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:45,490 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:46,446 - SimulationService - INFO - 📄 任务 3 子任务 3/10 (ID: 2ou02f67o4pmakY1hr8vjGJT):
2025-08-02 16:15:46,446 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:46,446 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:47,314 - SimulationService - INFO - 📄 任务 3 子任务 4/10 (ID: 2hvzI99uv4RZ91ikI4HIi8E):
2025-08-02 16:15:47,314 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:47,314 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:48,148 - SimulationService - INFO - 📄 任务 3 子任务 5/10 (ID: unX3Y4kz4wH9VE13CEswOOB):
2025-08-02 16:15:48,149 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:48,149 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:48,984 - SimulationService - INFO - 📄 任务 3 子任务 6/10 (ID: 3PM88pdMG4RqbuKFrSZEOhO):
2025-08-02 16:15:48,985 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:48,985 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:49,801 - SimulationService - INFO - 📄 任务 3 子任务 7/10 (ID: 2vdJuP25N4sK8LRKCucbLUU):
2025-08-02 16:15:49,802 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:49,803 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:51,185 - SimulationService - INFO - 📄 任务 3 子任务 8/10 (ID: 1rf2DZdrp57z9dpUBlsEX8r):
2025-08-02 16:15:51,185 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:51,185 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:52,043 - SimulationService - INFO - 📄 任务 3 子任务 9/10 (ID: 2XwFMubBI4LdaSKEkANfr9r):
2025-08-02 16:15:52,043 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:52,043 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:53,051 - SimulationService - INFO - 📄 任务 3 子任务 10/10 (ID: 1Cfrj0fBu4ZncoIGvfezXd5):
2025-08-02 16:15:53,052 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:53,052 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:53,999 - SimulationService - INFO - 任务 11 提交成功
2025-08-02 16:15:54,315 - SimulationService - ERROR - ❌ 任务 4 失败，包含的Alpha表达式：
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:15:54,316 - SimulationService - WARNING - ❌ 任务 4 失败https://api.worldquantbrain.com/simulations/17i8y02wB50hadT2MSn7RhS: {"children":["2bR5KL70v4IiaLG7JTBl8W4","Ljzxf14f4WM9nsyqvQNOZR","1louuR9p75dRamorHQ1HXEQ","4sc0VJ1yx4nZ9iZeZWqJ7Af","4ERObXe6e4Eg9V11ft8UwjKB","3HLAVm1Oa4EYb09193EzQ8LQ","4i4zRafm24L48LiezNvxLwg","3qBKqQgQ84X09whQJwxtpmo","8XCtJ7gn5ghaaYy4MNg5QY","172b9I8tJ4Un8PhVHkbRBDv"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:15:54,316 - SimulationService - INFO - 正在查询任务 4 的 10 个子任务详情...
2025-08-02 16:15:54,705 - SimulationService - INFO - 📄 任务 4 子任务 1/10 (ID: 2bR5KL70v4IiaLG7JTBl8W4):
2025-08-02 16:15:54,705 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:54,705 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:55,627 - SimulationService - INFO - 📄 任务 4 子任务 2/10 (ID: Ljzxf14f4WM9nsyqvQNOZR):
2025-08-02 16:15:55,627 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:55,627 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:56,653 - SimulationService - INFO - 📄 任务 4 子任务 3/10 (ID: 1louuR9p75dRamorHQ1HXEQ):
2025-08-02 16:15:56,653 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:56,653 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:57,777 - SimulationService - INFO - 📄 任务 4 子任务 4/10 (ID: 4sc0VJ1yx4nZ9iZeZWqJ7Af):
2025-08-02 16:15:57,777 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:57,778 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:58,699 - SimulationService - INFO - 📄 任务 4 子任务 5/10 (ID: 4ERObXe6e4Eg9V11ft8UwjKB):
2025-08-02 16:15:58,699 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:58,699 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:15:59,639 - SimulationService - INFO - 📄 任务 4 子任务 6/10 (ID: 3HLAVm1Oa4EYb09193EzQ8LQ):
2025-08-02 16:15:59,639 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:15:59,639 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:00,542 - SimulationService - INFO - 📄 任务 4 子任务 7/10 (ID: 4i4zRafm24L48LiezNvxLwg):
2025-08-02 16:16:00,542 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:00,543 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:01,464 - SimulationService - INFO - 📄 任务 4 子任务 8/10 (ID: 3qBKqQgQ84X09whQJwxtpmo):
2025-08-02 16:16:01,464 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:01,464 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:02,387 - SimulationService - INFO - 📄 任务 4 子任务 9/10 (ID: 8XCtJ7gn5ghaaYy4MNg5QY):
2025-08-02 16:16:02,387 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:02,387 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:03,231 - SimulationService - INFO - 📄 任务 4 子任务 10/10 (ID: 172b9I8tJ4Un8PhVHkbRBDv):
2025-08-02 16:16:03,232 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:03,232 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:04,843 - SimulationService - INFO - 任务 12 提交成功
2025-08-02 16:16:05,253 - SimulationService - ERROR - ❌ 任务 5 失败，包含的Alpha表达式：
2025-08-02 16:16:05,253 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:16:05,253 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:16:05,253 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, constant=0), Decay: 0
2025-08-02 16:16:05,253 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, constant=0), Decay: 0
2025-08-02 16:16:05,254 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, constant=0), Decay: 0
2025-08-02 16:16:05,254 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, constant=0), Decay: 0
2025-08-02 16:16:05,254 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, constant=0), Decay: 0
2025-08-02 16:16:05,254 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, driver='gaussian'), Decay: 0
2025-08-02 16:16:05,254 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, driver='gaussian'), Decay: 0
2025-08-02 16:16:05,254 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, driver='gaussian'), Decay: 0
2025-08-02 16:16:05,254 - SimulationService - WARNING - ❌ 任务 5 失败https://api.worldquantbrain.com/simulations/1bTV44dC84nnaCK1ewhVZBn: {"children":["1BG9Qq3gC5498zGcEgggZuc","47W1AHZr4lRcyO9PfKQmSe","3eslaMg3B4p9b2kjAez5iQh","1HpwPpaor4yR9MgcrpDdX7g","A5Qrw1tm5fLarei6fEecbT","1jxLuy3Me4qFay010zAlqhEL","HgsH94gw5719YpYXJArXK2","3BfLI46Z14R8cIZPKDNW3im","2OX239g7L5hu94LLcNmwZsZ","44bGt65AY4lybsnJmSi4V9T"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:16:05,254 - SimulationService - INFO - 正在查询任务 5 的 10 个子任务详情...
2025-08-02 16:16:05,748 - SimulationService - INFO - 📄 任务 5 子任务 1/10 (ID: 1BG9Qq3gC5498zGcEgggZuc):
2025-08-02 16:16:05,748 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:05,748 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:06,584 - SimulationService - INFO - 📄 任务 5 子任务 2/10 (ID: 47W1AHZr4lRcyO9PfKQmSe):
2025-08-02 16:16:06,584 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:06,584 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:07,710 - SimulationService - INFO - 📄 任务 5 子任务 3/10 (ID: 3eslaMg3B4p9b2kjAez5iQh):
2025-08-02 16:16:07,710 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:07,710 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:08,633 - SimulationService - INFO - 📄 任务 5 子任务 4/10 (ID: 1HpwPpaor4yR9MgcrpDdX7g):
2025-08-02 16:16:08,634 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:08,634 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:09,457 - SimulationService - INFO - 📄 任务 5 子任务 5/10 (ID: A5Qrw1tm5fLarei6fEecbT):
2025-08-02 16:16:09,458 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:09,458 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:10,293 - SimulationService - INFO - 📄 任务 5 子任务 6/10 (ID: 1jxLuy3Me4qFay010zAlqhEL):
2025-08-02 16:16:10,294 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:10,294 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:11,194 - SimulationService - INFO - 📄 任务 5 子任务 7/10 (ID: HgsH94gw5719YpYXJArXK2):
2025-08-02 16:16:11,195 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:11,195 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:12,019 - SimulationService - INFO - 📄 任务 5 子任务 8/10 (ID: 3BfLI46Z14R8cIZPKDNW3im):
2025-08-02 16:16:12,019 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:12,019 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:12,858 - SimulationService - INFO - 📄 任务 5 子任务 9/10 (ID: 2OX239g7L5hu94LLcNmwZsZ):
2025-08-02 16:16:12,858 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:12,858 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:13,689 - SimulationService - INFO - 📄 任务 5 子任务 10/10 (ID: 44bGt65AY4lybsnJmSi4V9T):
2025-08-02 16:16:13,689 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:13,690 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:14,813 - SimulationService - INFO - 任务 13 提交成功
2025-08-02 16:16:15,139 - SimulationService - ERROR - ❌ 任务 6 失败，包含的Alpha表达式：
2025-08-02 16:16:15,139 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, driver='gaussian'), Decay: 0
2025-08-02 16:16:15,139 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, driver='gaussian'), Decay: 0
2025-08-02 16:16:15,139 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:16:15,139 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:16:15,139 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:16:15,139 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:16:15,139 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:16:15,140 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:16:15,140 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:16:15,140 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:16:15,140 - SimulationService - WARNING - ❌ 任务 6 失败https://api.worldquantbrain.com/simulations/hSEPqemS4xk8zl9PT7ZOXB: {"children":["10c9Bv97w5eyb8AkrHqx1vK","4vpZDH8lK4BEcJgfJFDDYFD","32ox4z3vX4uN9sSRxKZA1KS","Hck8s6tR4QJatxuQfhElrn","3CAFNB3VP5aqaR11cq6sWE5i","2UFmRG7ui5cb8Vb3jpqopwf","31jh8jevg4F28ya2Oegyxo4","36xxprcVd4Nyc9cpWViiMkS","1K8jKM7Zb4Cc9DorNCfb96p","4fRc5c4Zz4R1blA28gevK0R"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:16:15,140 - SimulationService - INFO - 正在查询任务 6 的 10 个子任务详情...
2025-08-02 16:16:15,450 - SimulationService - INFO - 📄 任务 6 子任务 1/10 (ID: 10c9Bv97w5eyb8AkrHqx1vK):
2025-08-02 16:16:15,450 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:15,451 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:16,516 - SimulationService - INFO - 📄 任务 6 子任务 2/10 (ID: 4vpZDH8lK4BEcJgfJFDDYFD):
2025-08-02 16:16:16,517 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:16,517 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:17,931 - SimulationService - INFO - 📄 任务 6 子任务 3/10 (ID: 32ox4z3vX4uN9sSRxKZA1KS):
2025-08-02 16:16:17,931 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:17,931 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:18,769 - SimulationService - INFO - 📄 任务 6 子任务 4/10 (ID: Hck8s6tR4QJatxuQfhElrn):
2025-08-02 16:16:18,769 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:18,769 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:19,690 - SimulationService - INFO - 📄 任务 6 子任务 5/10 (ID: 3CAFNB3VP5aqaR11cq6sWE5i):
2025-08-02 16:16:19,690 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:19,691 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:20,637 - SimulationService - INFO - 📄 任务 6 子任务 6/10 (ID: 2UFmRG7ui5cb8Vb3jpqopwf):
2025-08-02 16:16:20,637 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:20,637 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:21,465 - SimulationService - INFO - 📄 任务 6 子任务 7/10 (ID: 31jh8jevg4F28ya2Oegyxo4):
2025-08-02 16:16:21,466 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:21,466 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:22,376 - SimulationService - INFO - 📄 任务 6 子任务 8/10 (ID: 36xxprcVd4Nyc9cpWViiMkS):
2025-08-02 16:16:22,376 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:22,376 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:23,239 - SimulationService - INFO - 📄 任务 6 子任务 9/10 (ID: 1K8jKM7Zb4Cc9DorNCfb96p):
2025-08-02 16:16:23,239 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:23,239 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:24,083 - SimulationService - INFO - 📄 任务 6 子任务 10/10 (ID: 4fRc5c4Zz4R1blA28gevK0R):
2025-08-02 16:16:24,083 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:24,083 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:25,045 - SimulationService - INFO - 任务 14 提交成功
2025-08-02 16:16:25,425 - SimulationService - ERROR - ❌ 任务 7 失败，包含的Alpha表达式：
2025-08-02 16:16:25,425 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, f=0.5), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, f=0.5), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, f=0.5), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, f=0.5), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, f=0.5), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, f=2), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, f=2), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, f=2), Decay: 0
2025-08-02 16:16:25,426 - SimulationService - WARNING - ❌ 任务 7 失败https://api.worldquantbrain.com/simulations/47qpCq7iv4kD9BGjQjugqvW: {"children":["1qAofLcxW4DGcHXd1JdTd4H","2G1hn6ba350P9zH6yv2a8E2","7DAW15UJ4yub4g1gr0FT5Y9","3cYJQ6RC4EMajG7Suz7iTu","1AB9B78QG4nSc8h1hzsHp3k0","1y7ayv59752LaA5ae7mETIS","QiLfHd2N4AMbsvfggWO30L","3wgQtN7es5dxbZddM1nKT4F","21aSDa4cF5afctHrlv3AKBt","38teQkeai4ktbOYhcPOONIw"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:16:25,426 - SimulationService - INFO - 正在查询任务 7 的 10 个子任务详情...
2025-08-02 16:16:25,757 - SimulationService - INFO - 📄 任务 7 子任务 1/10 (ID: 1qAofLcxW4DGcHXd1JdTd4H):
2025-08-02 16:16:25,757 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:25,757 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:26,756 - SimulationService - INFO - 📄 任务 7 子任务 2/10 (ID: 2G1hn6ba350P9zH6yv2a8E2):
2025-08-02 16:16:26,756 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:26,756 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:27,694 - SimulationService - INFO - 📄 任务 7 子任务 3/10 (ID: 7DAW15UJ4yub4g1gr0FT5Y9):
2025-08-02 16:16:27,694 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:27,694 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:28,702 - SimulationService - INFO - 📄 任务 7 子任务 4/10 (ID: 3cYJQ6RC4EMajG7Suz7iTu):
2025-08-02 16:16:28,702 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:28,702 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:29,735 - SimulationService - INFO - 📄 任务 7 子任务 5/10 (ID: 1AB9B78QG4nSc8h1hzsHp3k0):
2025-08-02 16:16:29,736 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:29,736 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:30,647 - SimulationService - INFO - 📄 任务 7 子任务 6/10 (ID: 1y7ayv59752LaA5ae7mETIS):
2025-08-02 16:16:30,647 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:30,647 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:31,569 - SimulationService - INFO - 📄 任务 7 子任务 7/10 (ID: QiLfHd2N4AMbsvfggWO30L):
2025-08-02 16:16:31,569 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:31,569 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:32,491 - SimulationService - INFO - 📄 任务 7 子任务 8/10 (ID: 3wgQtN7es5dxbZddM1nKT4F):
2025-08-02 16:16:32,491 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:32,491 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:33,349 - SimulationService - INFO - 📄 任务 7 子任务 9/10 (ID: 21aSDa4cF5afctHrlv3AKBt):
2025-08-02 16:16:33,349 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:33,349 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:34,333 - SimulationService - INFO - 📄 任务 7 子任务 10/10 (ID: 38teQkeai4ktbOYhcPOONIw):
2025-08-02 16:16:34,334 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:34,334 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:35,461 - SimulationService - INFO - 任务 15 提交成功
2025-08-02 16:16:36,382 - SimulationService - ERROR - ❌ 任务 8 失败，包含的Alpha表达式：
2025-08-02 16:16:36,382 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, f=2), Decay: 0
2025-08-02 16:16:36,382 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, f=2), Decay: 0
2025-08-02 16:16:36,382 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:16:36,383 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:16:36,383 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:16:36,383 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:16:36,383 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:16:36,383 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:16:36,383 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:16:36,383 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:16:36,384 - SimulationService - WARNING - ❌ 任务 8 失败https://api.worldquantbrain.com/simulations/2ys5P08sn4U2a5WjogD9KH0: {"children":["30C6OS5ng4kw9QR1dxKVJby3","4CFPNy6Iy53ea8b1foytMprY","30aMvv3mS4lMaEyJRlOGF","tInsWc9z4wE8Z5GjuzrMKX","2uEKEscd64hZaa26F23NLsf","wLmfl68F4AK9ANPdheMNfr","21wtAA5tx4jDaSAhSAGrX1t","2STBWS7994ybao813m3Y7fvf","3KAyY9a8s4COaqyRip4Reo2","22KqE11bI4znc39pXZZ3jUo"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:16:36,384 - SimulationService - INFO - 正在查询任务 8 的 10 个子任务详情...
2025-08-02 16:16:36,792 - SimulationService - INFO - 📄 任务 8 子任务 1/10 (ID: 30C6OS5ng4kw9QR1dxKVJby3):
2025-08-02 16:16:36,792 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:36,792 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:37,621 - SimulationService - INFO - 📄 任务 8 子任务 2/10 (ID: 4CFPNy6Iy53ea8b1foytMprY):
2025-08-02 16:16:37,622 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:37,622 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:38,446 - SimulationService - INFO - 📄 任务 8 子任务 3/10 (ID: 30aMvv3mS4lMaEyJRlOGF):
2025-08-02 16:16:38,446 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:38,447 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:39,281 - SimulationService - INFO - 📄 任务 8 子任务 4/10 (ID: tInsWc9z4wE8Z5GjuzrMKX):
2025-08-02 16:16:39,281 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:39,282 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:40,106 - SimulationService - INFO - 📄 任务 8 子任务 5/10 (ID: 2uEKEscd64hZaa26F23NLsf):
2025-08-02 16:16:40,106 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:40,106 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:40,989 - SimulationService - INFO - 📄 任务 8 子任务 6/10 (ID: wLmfl68F4AK9ANPdheMNfr):
2025-08-02 16:16:40,989 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:40,989 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:41,914 - SimulationService - INFO - 📄 任务 8 子任务 7/10 (ID: 21wtAA5tx4jDaSAhSAGrX1t):
2025-08-02 16:16:41,915 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:41,915 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:42,745 - SimulationService - INFO - 📄 任务 8 子任务 8/10 (ID: 2STBWS7994ybao813m3Y7fvf):
2025-08-02 16:16:42,746 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:42,746 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:43,652 - SimulationService - INFO - 📄 任务 8 子任务 9/10 (ID: 3KAyY9a8s4COaqyRip4Reo2):
2025-08-02 16:16:43,652 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:43,653 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:44,574 - SimulationService - INFO - 📄 任务 8 子任务 10/10 (ID: 22KqE11bI4znc39pXZZ3jUo):
2025-08-02 16:16:44,574 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:44,574 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:45,598 - SimulationService - INFO - 任务 16 提交成功
2025-08-02 16:16:45,978 - SimulationService - ERROR - ❌ 任务 9 失败，包含的Alpha表达式：
2025-08-02 16:16:45,978 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:16:45,978 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:16:45,978 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, dense=false), Decay: 0
2025-08-02 16:16:45,978 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, dense=false), Decay: 0
2025-08-02 16:16:45,978 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, dense=false), Decay: 0
2025-08-02 16:16:45,978 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, dense=false), Decay: 0
2025-08-02 16:16:45,978 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, dense=false), Decay: 0
2025-08-02 16:16:45,978 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, lag=0, rettype=0), Decay: 0
2025-08-02 16:16:45,978 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, lag=0, rettype=0), Decay: 0
2025-08-02 16:16:45,979 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, lag=0, rettype=0), Decay: 0
2025-08-02 16:16:45,979 - SimulationService - WARNING - ❌ 任务 9 失败https://api.worldquantbrain.com/simulations/3czHde93t54Q9FsPXefnq4l: {"children":["3swpc01iD4JRaVZbZwYVPMy","rkeaP5Kd5h49cddqJHgFX0","3wpuOv5714D19TIuMi9rs79","49Evbb1tA4ry953pOsnwlXK","lse7Pfns5drcwD1H1Ov0W6","8h57L1Zj4lZc0avA6nR3eb","2Je3xpbrx4MbbGm18n6aXENh","ItN1lgvU4DVauryCPllzef","3m3haPyO4xG8yWD9bzArbN","1jf2r2dWR4RGajv19JHPC59P"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:16:45,979 - SimulationService - INFO - 正在查询任务 9 的 10 个子任务详情...
2025-08-02 16:16:50,104 - SimulationService - INFO - 📄 任务 9 子任务 1/10 (ID: 3swpc01iD4JRaVZbZwYVPMy):
2025-08-02 16:16:50,105 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:50,105 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:51,025 - SimulationService - INFO - 📄 任务 9 子任务 2/10 (ID: rkeaP5Kd5h49cddqJHgFX0):
2025-08-02 16:16:51,025 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:51,025 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:52,253 - SimulationService - INFO - 📄 任务 9 子任务 3/10 (ID: 3wpuOv5714D19TIuMi9rs79):
2025-08-02 16:16:52,254 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:52,254 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:53,080 - SimulationService - INFO - 📄 任务 9 子任务 4/10 (ID: 49Evbb1tA4ry953pOsnwlXK):
2025-08-02 16:16:53,080 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:53,080 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:53,950 - SimulationService - INFO - 📄 任务 9 子任务 5/10 (ID: lse7Pfns5drcwD1H1Ov0W6):
2025-08-02 16:16:53,950 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:53,950 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:54,814 - SimulationService - INFO - 📄 任务 9 子任务 6/10 (ID: 8h57L1Zj4lZc0avA6nR3eb):
2025-08-02 16:16:54,814 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:54,814 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:56,349 - SimulationService - INFO - 📄 任务 9 子任务 7/10 (ID: 2Je3xpbrx4MbbGm18n6aXENh):
2025-08-02 16:16:56,350 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:56,350 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:57,577 - SimulationService - INFO - 📄 任务 9 子任务 8/10 (ID: ItN1lgvU4DVauryCPllzef):
2025-08-02 16:16:57,577 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:57,577 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:58,500 - SimulationService - INFO - 📄 任务 9 子任务 9/10 (ID: 3m3haPyO4xG8yWD9bzArbN):
2025-08-02 16:16:58,501 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:58,501 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:16:59,354 - SimulationService - INFO - 📄 任务 9 子任务 10/10 (ID: 1jf2r2dWR4RGajv19JHPC59P):
2025-08-02 16:16:59,354 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:16:59,354 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:00,319 - SimulationService - INFO - 任务 17 提交成功
2025-08-02 16:17:00,958 - SimulationService - ERROR - ❌ 任务 10 失败，包含的Alpha表达式：
2025-08-02 16:17:00,958 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, lag=0, rettype=0), Decay: 0
2025-08-02 16:17:00,958 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, lag=0, rettype=0), Decay: 0
2025-08-02 16:17:00,958 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:17:00,958 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:17:00,958 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:17:00,958 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:17:00,959 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:17:00,959 - SimulationService - ERROR -    - Alpha: ts_target_tvr_decay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), lambda_min=0, lambda_max=1, target_tvr=0.1), Decay: 0
2025-08-02 16:17:00,959 - SimulationService - ERROR -    - Alpha: ts_product(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:17:00,959 - SimulationService - ERROR -    - Alpha: ts_product(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:17:00,959 - SimulationService - WARNING - ❌ 任务 10 失败https://api.worldquantbrain.com/simulations/3FrXJibSN5kf9V79CE89yI3: {"children":["3fzZzG83s5k8c6AlAz6Holk","1DiaSDbN25gocoDyw7iJ3R1","2q9ShD33u4nzcmM15Ns3poVF","2Fve2CdX15bdcxQjJxVY96f","49D29bfiz5hUc5X3wO3chAT","3emYBk5F04oK91I3boWwsgn","JnCWF6oP4YKbnixHs809Ej","aiFJ09o84RgaEYRtAQypV2","4zz2L45SY52OcnjZnUwbbyY","2abR9K5aX56DaVe16gbliQHR"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:17:00,959 - SimulationService - INFO - 正在查询任务 10 的 10 个子任务详情...
2025-08-02 16:17:01,301 - SimulationService - INFO - 📄 任务 10 子任务 1/10 (ID: 3fzZzG83s5k8c6AlAz6Holk):
2025-08-02 16:17:01,301 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:01,301 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:02,220 - SimulationService - INFO - 📄 任务 10 子任务 2/10 (ID: 1DiaSDbN25gocoDyw7iJ3R1):
2025-08-02 16:17:02,221 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:02,221 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:03,052 - SimulationService - INFO - 📄 任务 10 子任务 3/10 (ID: 2q9ShD33u4nzcmM15Ns3poVF):
2025-08-02 16:17:03,052 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:03,052 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:03,943 - SimulationService - INFO - 📄 任务 10 子任务 4/10 (ID: 2Fve2CdX15bdcxQjJxVY96f):
2025-08-02 16:17:03,944 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:03,944 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:04,951 - SimulationService - INFO - 📄 任务 10 子任务 5/10 (ID: 49D29bfiz5hUc5X3wO3chAT):
2025-08-02 16:17:04,951 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:04,951 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:05,788 - SimulationService - INFO - 📄 任务 10 子任务 6/10 (ID: 3emYBk5F04oK91I3boWwsgn):
2025-08-02 16:17:05,789 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:05,789 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:06,868 - SimulationService - INFO - 📄 任务 10 子任务 7/10 (ID: JnCWF6oP4YKbnixHs809Ej):
2025-08-02 16:17:06,868 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:06,869 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:07,721 - SimulationService - INFO - 📄 任务 10 子任务 8/10 (ID: aiFJ09o84RgaEYRtAQypV2):
2025-08-02 16:17:07,722 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:07,722 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:08,637 - SimulationService - INFO - 📄 任务 10 子任务 9/10 (ID: 4zz2L45SY52OcnjZnUwbbyY):
2025-08-02 16:17:08,638 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:08,638 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:09,670 - SimulationService - INFO - 📄 任务 10 子任务 10/10 (ID: 2abR9K5aX56DaVe16gbliQHR):
2025-08-02 16:17:09,671 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:09,671 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:10,799 - SimulationService - INFO - 任务 18 提交成功
2025-08-02 16:17:11,114 - SimulationService - ERROR - ❌ 任务 11 失败，包含的Alpha表达式：
2025-08-02 16:17:11,114 - SimulationService - ERROR -    - Alpha: ts_product(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:17:11,114 - SimulationService - ERROR -    - Alpha: ts_product(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:17:11,114 - SimulationService - ERROR -    - Alpha: ts_product(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:17:11,114 - SimulationService - ERROR -    - Alpha: winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), Decay: 0
2025-08-02 16:17:11,114 - SimulationService - ERROR -    - Alpha: reverse(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:17:11,114 - SimulationService - ERROR -    - Alpha: inverse(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:17:11,114 - SimulationService - ERROR -    - Alpha: rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:17:11,115 - SimulationService - ERROR -    - Alpha: zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:17:11,115 - SimulationService - ERROR -    - Alpha: quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:17:11,115 - SimulationService - ERROR -    - Alpha: normalize(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:17:11,115 - SimulationService - WARNING - ❌ 任务 11 失败https://api.worldquantbrain.com/simulations/zh2DK7BO4lqb3J1bStJLEUD: {"children":["3mUnLsaEM5afbWSbPS10dOL","4zWvWRd7W4n8aPjiBRkAzog","4j5eD0g3A4lMaO81ehX2j6DW","NA8rqbuQ4z79mkVLkqzFM9","1PdCt6fzy4rFboG9WAiJTBE","3Gw7cpbiN4kcbk0ww94wo58","2Qqzz8eIF4Rx9z213yaIAViL","3F9d5ygiQ4FbaFQ1cLPZoU6R","1roRRA19k4sj9aW1pUoSUgH","T1PGm59R4mqaa8lzN1YAP2"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:17:11,115 - SimulationService - INFO - 正在查询任务 11 的 10 个子任务详情...
2025-08-02 16:17:11,461 - SimulationService - INFO - 📄 任务 11 子任务 1/10 (ID: 3mUnLsaEM5afbWSbPS10dOL):
2025-08-02 16:17:11,461 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:11,461 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:12,711 - SimulationService - INFO - 📄 任务 11 子任务 2/10 (ID: 4zWvWRd7W4n8aPjiBRkAzog):
2025-08-02 16:17:12,711 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:12,711 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:13,553 - SimulationService - INFO - 📄 任务 11 子任务 3/10 (ID: 4j5eD0g3A4lMaO81ehX2j6DW):
2025-08-02 16:17:13,553 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:13,553 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:14,394 - SimulationService - INFO - 📄 任务 11 子任务 4/10 (ID: NA8rqbuQ4z79mkVLkqzFM9):
2025-08-02 16:17:14,394 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:14,394 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:15,363 - SimulationService - INFO - 📄 任务 11 子任务 5/10 (ID: 1PdCt6fzy4rFboG9WAiJTBE):
2025-08-02 16:17:15,364 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:15,364 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:16,215 - SimulationService - INFO - 📄 任务 11 子任务 6/10 (ID: 3Gw7cpbiN4kcbk0ww94wo58):
2025-08-02 16:17:16,215 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:16,215 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:17,138 - SimulationService - INFO - 📄 任务 11 子任务 7/10 (ID: 2Qqzz8eIF4Rx9z213yaIAViL):
2025-08-02 16:17:17,138 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:17,138 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:17,966 - SimulationService - INFO - 📄 任务 11 子任务 8/10 (ID: 3F9d5ygiQ4FbaFQ1cLPZoU6R):
2025-08-02 16:17:17,966 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:17,966 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:18,877 - SimulationService - INFO - 📄 任务 11 子任务 9/10 (ID: 1roRRA19k4sj9aW1pUoSUgH):
2025-08-02 16:17:18,878 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:18,878 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:20,004 - SimulationService - INFO - 📄 任务 11 子任务 10/10 (ID: T1PGm59R4mqaa8lzN1YAP2):
2025-08-02 16:17:20,004 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:20,004 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:21,028 - SimulationService - INFO - 任务 19 提交成功
2025-08-02 16:17:21,437 - SimulationService - ERROR - ❌ 任务 12 失败，包含的Alpha表达式：
2025-08-02 16:17:21,437 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, constant=0), Decay: 0
2025-08-02 16:17:21,437 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, constant=0), Decay: 0
2025-08-02 16:17:21,437 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, constant=0), Decay: 0
2025-08-02 16:17:21,437 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, constant=0), Decay: 0
2025-08-02 16:17:21,438 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, constant=0), Decay: 0
2025-08-02 16:17:21,438 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:17:21,438 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:17:21,438 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:17:21,438 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:17:21,438 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_mfmq_ibcomq,500), ts_zscore(fnd6_currencya_curcd,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:17:21,438 - SimulationService - WARNING - ❌ 任务 12 失败https://api.worldquantbrain.com/simulations/1rfq1m1Hu5hXbYZ1fS1uIhik: {"children":["40x2AC3Ls5i59VNzylIQz6y","4vQF7K7qa4pOcjeRmgeRd4n","2nsERn18Z5kfchp1o1OCPE3","2Q8KkjaAY4Vhbcf11YljLfGM","45UM5g9P64JMb1GHvmIvAQb","3Bd9cXbtZ4G4cD8X127NviC","1Iwo2MecK4sNaODiRrNLLaM","trzVu79K4v18KTh0AIsuEY","3jrynF5ng4TGbXfx5BWLtA7","1v82E0fDj4YWbF2AsdZKfxl"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:17:21,438 - SimulationService - INFO - 正在查询任务 12 的 10 个子任务详情...
2025-08-02 16:17:21,849 - SimulationService - INFO - 📄 任务 12 子任务 1/10 (ID: 40x2AC3Ls5i59VNzylIQz6y):
2025-08-02 16:17:21,849 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:21,849 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:22,673 - SimulationService - INFO - 📄 任务 12 子任务 2/10 (ID: 4vQF7K7qa4pOcjeRmgeRd4n):
2025-08-02 16:17:22,673 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:22,673 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:23,511 - SimulationService - INFO - 📄 任务 12 子任务 3/10 (ID: 2nsERn18Z5kfchp1o1OCPE3):
2025-08-02 16:17:23,511 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:23,511 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:24,407 - SimulationService - INFO - 📄 任务 12 子任务 4/10 (ID: 2Q8KkjaAY4Vhbcf11YljLfGM):
2025-08-02 16:17:24,407 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:24,407 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:25,233 - SimulationService - INFO - 📄 任务 12 子任务 5/10 (ID: 45UM5g9P64JMb1GHvmIvAQb):
2025-08-02 16:17:25,233 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:25,234 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:26,073 - SimulationService - INFO - 📄 任务 12 子任务 6/10 (ID: 3Bd9cXbtZ4G4cD8X127NviC):
2025-08-02 16:17:26,073 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:17:26,073 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:17:26,686 - ReplicatedAlphaMachine - INFO - 开始清理资源
2025-08-02 16:17:26,687 - ReplicatedAlphaMachine - INFO - 等待异步数据库操作完成
2025-08-02 16:17:26,687 - AsyncDatabaseService - INFO - 所有异步数据库操作已完成
2025-08-02 16:17:26,688 - AsyncDatabaseService - INFO - 开始关闭异步数据库服务
2025-08-02 16:17:26,688 - AsyncDatabaseService - INFO - 线程池已关闭
2025-08-02 16:17:26,696 - AsyncDatabaseService - INFO - 所有线程数据库连接已关闭
2025-08-02 16:17:26,696 - AsyncDatabaseService - INFO - 异步数据库服务已关闭
2025-08-02 16:17:26,696 - ReplicatedAlphaMachine - INFO - 数据库连接已关闭
2025-08-02 16:17:26,697 - ReplicatedAlphaMachine - INFO - 资源清理完成
