2025-08-02 16:18:18,925 - ReplicatedAlphaMachine - INFO - 日志系统初始化完成，日志文件: logs/replicated_alpha_machine_2025-08-02_16-18-18.log
2025-08-02 16:18:18,925 - ReplicatedAlphaMachine - INFO - 加载配置文件
2025-08-02 16:18:18,925 - root - INFO - dataset_id: model26
2025-08-02 16:18:18,925 - root - INFO - region: USA
2025-08-02 16:18:18,925 - root - INFO - universe: TOP3000
2025-08-02 16:18:18,925 - root - INFO - delay: 1
2025-08-02 16:18:18,925 - root - INFO - decay: 0
2025-08-02 16:18:18,925 - root - INFO - neutralization: SUBINDUSTRY
2025-08-02 16:18:18,925 - root - INFO - prefix: winsorize
2025-08-02 16:18:18,925 - root - INFO - coverage: 0.8
2025-08-02 16:18:18,925 - root - INFO - truncation: 0.08
2025-08-02 16:18:18,925 - root - INFO - instrument_type: EQUITY
2025-08-02 16:18:18,925 - root - INFO - pasteurization: ON
2025-08-02 16:18:18,925 - root - INFO - nan_handling: OFF
2025-08-02 16:18:18,925 - root - INFO - unit_handling: VERIFY
2025-08-02 16:18:18,925 - root - INFO - language: FASTEXPR
2025-08-02 16:18:18,925 - root - INFO - visualization: false
2025-08-02 16:18:18,925 - root - INFO - max_trade: OFF
2025-08-02 16:18:18,926 - root - INFO - test_period: P0Y
2025-08-02 16:18:18,926 - root - INFO - events: open_events
2025-08-02 16:18:18,926 - root - INFO - alpha_num_filter: 2000
2025-08-02 16:18:18,926 - root - INFO - alpha_num_submit: 500
2025-08-02 16:18:18,926 - root - INFO - third_promote_num: 200
2025-08-02 16:18:18,926 - root - INFO - batch_size: 500
2025-08-02 16:18:18,926 - root - INFO - second_promote_sharp: 1.2
2025-08-02 16:18:18,926 - root - INFO - second_promote_fitness: 0.7
2025-08-02 16:18:18,926 - root - INFO - third_promote_sharp: 1.3
2025-08-02 16:18:18,926 - root - INFO - third_promote_fitness: 0.8
2025-08-02 16:18:18,926 - root - INFO - submit_sharp_th: 1.58
2025-08-02 16:18:18,926 - root - INFO - submit_fitness_th: 1.0
2025-08-02 16:18:18,926 - root - INFO - submit_margin_th: 1.0
2025-08-02 16:18:18,926 - root - INFO - is_simulate: True
2025-08-02 16:18:18,926 - root - INFO - is_second_promote: True
2025-08-02 16:18:18,926 - root - INFO - is_third_promote: True
2025-08-02 16:18:18,926 - root - INFO - is_prune: False
2025-08-02 16:18:18,926 - root - INFO - fields_file: filtered_shuffled_expressions.txt
2025-08-02 16:18:18,926 - root - INFO - is_filter_pnl: False
2025-08-02 16:18:18,926 - root - INFO - is_preprocess_alpha: True
2025-08-02 16:18:18,926 - ReplicatedAlphaMachine - INFO - 初始化数据库服务
2025-08-02 16:18:18,926 - root - INFO - 初始化数据库连接
2025-08-02 16:18:18,927 - root - INFO - 数据库连接成功
2025-08-02 16:18:18,928 - root - INFO - 数据库表创建/检查完成
2025-08-02 16:18:18,928 - root - INFO - check_status 字段已存在，跳过迁移
2025-08-02 16:18:18,928 - root - INFO - 数据库初始化完成
2025-08-02 16:18:18,928 - root - INFO - 创建异步数据库服务
2025-08-02 16:18:18,928 - AsyncDatabaseService - INFO - 异步数据库服务已初始化，线程池大小: 3
2025-08-02 16:18:18,929 - ReplicatedAlphaMachine - INFO - 初始化认证服务
2025-08-02 16:18:18,929 - ReplicatedAlphaMachine - INFO - 初始化业务服务
2025-08-02 16:18:18,929 - ReplicatedAlphaMachine - INFO - Alpha机器初始化完成
2025-08-02 16:18:18,929 - ReplicatedAlphaMachine - INFO - 开始Alpha生成和仿真流程
2025-08-02 16:18:18,929 - ReplicatedAlphaMachine - INFO - 开始生成Alpha表达式
2025-08-02 16:18:18,929 - AlphaService - INFO - 从文件 converted_expressions.txt 创建Alpha
2025-08-02 16:18:18,929 - AlphaService - INFO - 从文件加载了 44 个数据字段
2025-08-02 16:18:18,930 - AlphaFactory - INFO - 生成了 4972 个一阶Alpha表达式
2025-08-02 16:18:18,930 - AlphaService - INFO - 生成了 4972 个Alpha表达式
2025-08-02 16:18:18,930 - ReplicatedAlphaMachine - INFO - 生成了 4972 个Alpha，开始过滤已回测的Alpha
2025-08-02 16:18:18,954 - root - INFO - 已过滤掉 0 个已回测的alpha，剩余 4972 个新alpha待回测
2025-08-02 16:18:18,954 - ReplicatedAlphaMachine - INFO - 过滤后剩余 4972 个未回测的Alpha
2025-08-02 16:18:18,954 - ReplicatedAlphaMachine - INFO - 执行日期范围: 12-01 到 12-07
2025-08-02 16:18:18,954 - ReplicatedAlphaMachine - INFO - 开始批量仿真 4972 个Alpha
2025-08-02 16:18:18,954 - SimulationService - INFO - 开始批量仿真，批次大小: 500
2025-08-02 16:18:18,954 - SimulationService - INFO - 进行第 1/10 批仿真，数量: 500
2025-08-02 16:18:18,954 - SimulationService - INFO - 开始仿真 500 个Alpha
2025-08-02 16:18:18,954 - SimulationService - INFO - 准备仿真 50 个任务
2025-08-02 16:18:20,476 - root - INFO - 登录成功
2025-08-02 16:18:20,477 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-02 16:18:20,928 - SimulationService - INFO - 任务 0 提交成功
2025-08-02 16:18:21,411 - SimulationService - INFO - 任务 1 提交成功
2025-08-02 16:18:22,212 - SimulationService - INFO - 任务 2 提交成功
2025-08-02 16:18:22,686 - SimulationService - INFO - 任务 3 提交成功
2025-08-02 16:18:23,389 - SimulationService - INFO - 任务 4 提交成功
2025-08-02 16:18:23,928 - SimulationService - INFO - 任务 5 提交成功
2025-08-02 16:18:24,419 - SimulationService - INFO - 任务 6 提交成功
2025-08-02 16:18:25,069 - SimulationService - INFO - 任务 7 提交成功
2025-08-02 16:18:33,109 - SimulationService - ERROR - ❌ 任务 6 失败，包含的Alpha表达式：
2025-08-02 16:18:33,109 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120, driver='gaussian'), Decay: 0
2025-08-02 16:18:33,109 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240, driver='gaussian'), Decay: 0
2025-08-02 16:18:33,109 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5), Decay: 0
2025-08-02 16:18:33,109 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22), Decay: 0
2025-08-02 16:18:33,109 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 66), Decay: 0
2025-08-02 16:18:33,109 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120), Decay: 0
2025-08-02 16:18:33,109 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240), Decay: 0
2025-08-02 16:18:33,109 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5), Decay: 0
2025-08-02 16:18:33,109 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22), Decay: 0
2025-08-02 16:18:33,110 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 66), Decay: 0
2025-08-02 16:18:33,110 - SimulationService - WARNING - ❌ 任务 6 失败https://api.worldquantbrain.com/simulations/4koendgB5jMbLngmkXybYQ: {"children":["4rjTOmfY65hTbvYjyY0PcyK","2HKK7k3SM4KVchdugxCINMS","4jNsBSfMQ534btC8K8h7qek","4uK6iX5jo5he9W5X6O9mi2t","1q4UJwcEe4vibIXZeM7ckVt","gEqWE7cT5ddaoAVAHpQJGG","2wa4kGbsh4Zl8yhcyGsti0V","2VRa5Z7MW5jt8XQ1afsGCqJ9","2JPoDcbva55FcBJZ0Jul2N","GR0cQbKU5fL9vUqQ16dUwN"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:18:33,110 - SimulationService - INFO - 正在查询任务 6 的 10 个子任务详情...
2025-08-02 16:18:34,140 - SimulationService - INFO - 📄 任务 6 子任务 1/10 (ID: 4rjTOmfY65hTbvYjyY0PcyK):
2025-08-02 16:18:34,140 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:18:35,062 - SimulationService - INFO - 📄 任务 6 子任务 2/10 (ID: 2HKK7k3SM4KVchdugxCINMS):
2025-08-02 16:18:35,063 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:18:35,914 - SimulationService - INFO - 📄 任务 6 子任务 3/10 (ID: 4jNsBSfMQ534btC8K8h7qek):
2025-08-02 16:18:35,915 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:35,915 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:18:36,906 - SimulationService - INFO - 📄 任务 6 子任务 4/10 (ID: 4uK6iX5jo5he9W5X6O9mi2t):
2025-08-02 16:18:36,907 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:36,907 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:18:37,861 - SimulationService - INFO - 📄 任务 6 子任务 5/10 (ID: 1q4UJwcEe4vibIXZeM7ckVt):
2025-08-02 16:18:37,861 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:37,861 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:18:38,851 - SimulationService - INFO - 📄 任务 6 子任务 6/10 (ID: gEqWE7cT5ddaoAVAHpQJGG):
2025-08-02 16:18:38,851 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:38,851 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:18:40,489 - SimulationService - INFO - 📄 任务 6 子任务 7/10 (ID: 2wa4kGbsh4Zl8yhcyGsti0V):
2025-08-02 16:18:40,490 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:40,490 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:18:41,551 - SimulationService - INFO - 📄 任务 6 子任务 8/10 (ID: 2VRa5Z7MW5jt8XQ1afsGCqJ9):
2025-08-02 16:18:41,551 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:18:42,426 - SimulationService - INFO - 📄 任务 6 子任务 9/10 (ID: 2JPoDcbva55FcBJZ0Jul2N):
2025-08-02 16:18:42,426 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:18:43,265 - SimulationService - INFO - 📄 任务 6 子任务 10/10 (ID: GR0cQbKU5fL9vUqQ16dUwN):
2025-08-02 16:18:43,265 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:18:44,278 - SimulationService - INFO - 任务 8 提交成功
2025-08-02 16:18:44,688 - SimulationService - ERROR - ❌ 任务 7 失败，包含的Alpha表达式：
2025-08-02 16:18:44,688 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120), Decay: 0
2025-08-02 16:18:44,688 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240), Decay: 0
2025-08-02 16:18:44,688 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5, f=0.5), Decay: 0
2025-08-02 16:18:44,688 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22, f=0.5), Decay: 0
2025-08-02 16:18:44,689 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 66, f=0.5), Decay: 0
2025-08-02 16:18:44,689 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120, f=0.5), Decay: 0
2025-08-02 16:18:44,689 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240, f=0.5), Decay: 0
2025-08-02 16:18:44,689 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5, f=2), Decay: 0
2025-08-02 16:18:44,689 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22, f=2), Decay: 0
2025-08-02 16:18:44,689 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 66, f=2), Decay: 0
2025-08-02 16:18:44,689 - SimulationService - WARNING - ❌ 任务 7 失败https://api.worldquantbrain.com/simulations/pPWMJ5XZ4GR9YO3UMNSBP3: {"children":["2KTRDdgn1579bijHWlUpNG8","3rOoICbiO4yu8ZLUYgVHCvw","4hUZy2Mk4kl8Ok18Gs5RpPB","3TiJPqcOE4liahH12U65qNQN","3fmuInerY4HicsKn6c1oKWB","1a1X8yaUw4jl9fBJTeaySH2","2TpAS6b0B55Ybby8JFai6Ud","1Ywv8r8MD5hWa6J5Xp2AreG","2tfmh91ml4E08O5ZRzd9fr9","4kTGyL7LM5aKc9AXpTd6kbk"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:18:44,689 - SimulationService - INFO - 正在查询任务 7 的 10 个子任务详情...
2025-08-02 16:18:45,097 - SimulationService - INFO - 📄 任务 7 子任务 1/10 (ID: 2KTRDdgn1579bijHWlUpNG8):
2025-08-02 16:18:45,097 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:18:46,224 - SimulationService - INFO - 📄 任务 7 子任务 2/10 (ID: 3rOoICbiO4yu8ZLUYgVHCvw):
2025-08-02 16:18:46,225 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:18:47,452 - SimulationService - INFO - 📄 任务 7 子任务 3/10 (ID: 4hUZy2Mk4kl8Ok18Gs5RpPB):
2025-08-02 16:18:47,453 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:47,453 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:18:48,279 - SimulationService - INFO - 📄 任务 7 子任务 4/10 (ID: 3TiJPqcOE4liahH12U65qNQN):
2025-08-02 16:18:48,279 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:48,279 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:18:49,193 - SimulationService - INFO - 📄 任务 7 子任务 5/10 (ID: 3fmuInerY4HicsKn6c1oKWB):
2025-08-02 16:18:49,193 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:49,193 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:18:50,115 - SimulationService - INFO - 📄 任务 7 子任务 6/10 (ID: 1a1X8yaUw4jl9fBJTeaySH2):
2025-08-02 16:18:50,115 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:50,115 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:18:51,038 - SimulationService - INFO - 📄 任务 7 子任务 7/10 (ID: 2TpAS6b0B55Ybby8JFai6Ud):
2025-08-02 16:18:51,038 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:51,038 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:18:51,977 - SimulationService - INFO - 📄 任务 7 子任务 8/10 (ID: 1Ywv8r8MD5hWa6J5Xp2AreG):
2025-08-02 16:18:51,978 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:51,978 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:18:52,828 - SimulationService - INFO - 📄 任务 7 子任务 9/10 (ID: 2tfmh91ml4E08O5ZRzd9fr9):
2025-08-02 16:18:52,828 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:52,828 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:18:53,708 - SimulationService - INFO - 📄 任务 7 子任务 10/10 (ID: 4kTGyL7LM5aKc9AXpTd6kbk):
2025-08-02 16:18:53,708 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:53,708 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:18:54,722 - SimulationService - INFO - 任务 9 提交成功
2025-08-02 16:18:55,132 - SimulationService - ERROR - ❌ 任务 8 失败，包含的Alpha表达式：
2025-08-02 16:18:55,132 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120, f=2), Decay: 0
2025-08-02 16:18:55,132 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240, f=2), Decay: 0
2025-08-02 16:18:55,132 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5), Decay: 0
2025-08-02 16:18:55,132 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22), Decay: 0
2025-08-02 16:18:55,133 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 66), Decay: 0
2025-08-02 16:18:55,133 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120), Decay: 0
2025-08-02 16:18:55,133 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240), Decay: 0
2025-08-02 16:18:55,133 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5), Decay: 0
2025-08-02 16:18:55,133 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22), Decay: 0
2025-08-02 16:18:55,133 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 66), Decay: 0
2025-08-02 16:18:55,133 - SimulationService - WARNING - ❌ 任务 8 失败https://api.worldquantbrain.com/simulations/4zzG4HdFZ4Lubkzt1v5clSi: {"children":["NslzF2mm4Rx9bWFb04rcLA","4n3RhOe994rr8Up1czy1zSrN","23rZj36Cf5es9PMjgw0VUKI","2IM8hE5b05ctbmQba0QIs25","1zDs407VW4Nh9sCsHAP8fdH","b9Ro4bWu4wsbrh150IhdavM","30TUoBgcL4TS8SGBZJoFhvf","2Q9chxdFv4EM9oV175xxQCbw","2LvX5gghW4iKbwD1dlmFop7B","178EnRaBY5fxaMd1gs4aa07i"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:18:55,133 - SimulationService - INFO - 正在查询任务 8 的 10 个子任务详情...
2025-08-02 16:18:55,904 - SimulationService - INFO - 📄 任务 8 子任务 1/10 (ID: NslzF2mm4Rx9bWFb04rcLA):
2025-08-02 16:18:55,904 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:55,904 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:18:56,781 - SimulationService - INFO - 📄 任务 8 子任务 2/10 (ID: 4n3RhOe994rr8Up1czy1zSrN):
2025-08-02 16:18:56,781 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:18:56,781 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:18:57,692 - SimulationService - INFO - 📄 任务 8 子任务 3/10 (ID: 23rZj36Cf5es9PMjgw0VUKI):
2025-08-02 16:18:57,693 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:18:58,614 - SimulationService - INFO - 📄 任务 8 子任务 4/10 (ID: 2IM8hE5b05ctbmQba0QIs25):
2025-08-02 16:18:58,614 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:18:59,537 - SimulationService - INFO - 📄 任务 8 子任务 5/10 (ID: 1zDs407VW4Nh9sCsHAP8fdH):
2025-08-02 16:18:59,538 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:00,612 - SimulationService - INFO - 📄 任务 8 子任务 6/10 (ID: b9Ro4bWu4wsbrh150IhdavM):
2025-08-02 16:19:00,612 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:01,584 - SimulationService - INFO - 📄 任务 8 子任务 7/10 (ID: 30TUoBgcL4TS8SGBZJoFhvf):
2025-08-02 16:19:01,585 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:02,609 - SimulationService - INFO - 📄 任务 8 子任务 8/10 (ID: 2Q9chxdFv4EM9oV175xxQCbw):
2025-08-02 16:19:02,610 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:02,610 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:19:03,462 - SimulationService - INFO - 📄 任务 8 子任务 9/10 (ID: 2LvX5gghW4iKbwD1dlmFop7B):
2025-08-02 16:19:03,463 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:03,463 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:19:04,348 - SimulationService - INFO - 📄 任务 8 子任务 10/10 (ID: 178EnRaBY5fxaMd1gs4aa07i):
2025-08-02 16:19:04,348 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:04,348 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:19:05,372 - SimulationService - INFO - 任务 10 提交成功
2025-08-02 16:19:05,991 - SimulationService - ERROR - ❌ 任务 9 失败，包含的Alpha表达式：
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5, dense=false), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22, dense=false), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 66, dense=false), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120, dense=false), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240, dense=false), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5, lag=0, rettype=0), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22, lag=0, rettype=0), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 66, lag=0, rettype=0), Decay: 0
2025-08-02 16:19:05,991 - SimulationService - WARNING - ❌ 任务 9 失败https://api.worldquantbrain.com/simulations/2mVgUic254Mkbwo1dwhkojYW: {"children":["1FPmxd3q85a5aUYMSmhp4CT","3HybDZeap4Ve9lBrx0K9Plu","T1yEl24n4TqbnvM3YgbltE","4tolgE8TF4RI9osKpuufafY","3s6aUm8Bc4Oha9cDyVu5cmK","1RLl7R9g4mAaAkNYF1NJTP","1gR7yf5s95h496CQep4p8cI","2bAtVY4N94DF9jc7DYf7Lxo","2WDS7k4O54UQciZ1cgrFpNrA","4kvHPU11H4EI9LT1hbCSl82W"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:19:05,991 - SimulationService - INFO - 正在查询任务 9 的 10 个子任务详情...
2025-08-02 16:19:06,430 - SimulationService - INFO - 📄 任务 9 子任务 1/10 (ID: 1FPmxd3q85a5aUYMSmhp4CT):
2025-08-02 16:19:06,430 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:06,430 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:19:07,626 - SimulationService - INFO - 📄 任务 9 子任务 2/10 (ID: 3HybDZeap4Ve9lBrx0K9Plu):
2025-08-02 16:19:07,627 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:07,627 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:19:08,453 - SimulationService - INFO - 📄 任务 9 子任务 3/10 (ID: T1yEl24n4TqbnvM3YgbltE):
2025-08-02 16:19:08,454 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:09,383 - SimulationService - INFO - 📄 任务 9 子任务 4/10 (ID: 4tolgE8TF4RI9osKpuufafY):
2025-08-02 16:19:09,383 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:10,216 - SimulationService - INFO - 📄 任务 9 子任务 5/10 (ID: 3s6aUm8Bc4Oha9cDyVu5cmK):
2025-08-02 16:19:10,217 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:11,044 - SimulationService - INFO - 📄 任务 9 子任务 6/10 (ID: 1RLl7R9g4mAaAkNYF1NJTP):
2025-08-02 16:19:11,044 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:11,927 - SimulationService - INFO - 📄 任务 9 子任务 7/10 (ID: 1gR7yf5s95h496CQep4p8cI):
2025-08-02 16:19:11,927 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:12,750 - SimulationService - INFO - 📄 任务 9 子任务 8/10 (ID: 2bAtVY4N94DF9jc7DYf7Lxo):
2025-08-02 16:19:12,750 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:13,922 - SimulationService - INFO - 📄 任务 9 子任务 9/10 (ID: 2WDS7k4O54UQciZ1cgrFpNrA):
2025-08-02 16:19:13,922 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:14,742 - SimulationService - INFO - 📄 任务 9 子任务 10/10 (ID: 4kvHPU11H4EI9LT1hbCSl82W):
2025-08-02 16:19:14,743 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:15,817 - SimulationService - INFO - 任务 11 提交成功
2025-08-02 16:19:16,228 - SimulationService - ERROR - ❌ 任务 10 失败，包含的Alpha表达式：
2025-08-02 16:19:16,228 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120, lag=0, rettype=0), Decay: 0
2025-08-02 16:19:16,228 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240, lag=0, rettype=0), Decay: 0
2025-08-02 16:19:16,228 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5), Decay: 0
2025-08-02 16:19:16,229 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22), Decay: 0
2025-08-02 16:19:16,229 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 66), Decay: 0
2025-08-02 16:19:16,229 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 120), Decay: 0
2025-08-02 16:19:16,229 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 240), Decay: 0
2025-08-02 16:19:16,229 - SimulationService - ERROR -    - Alpha: ts_target_tvr_decay(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), lambda_min=0, lambda_max=1, target_tvr=0.1), Decay: 0
2025-08-02 16:19:16,229 - SimulationService - ERROR -    - Alpha: ts_product(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 5), Decay: 0
2025-08-02 16:19:16,229 - SimulationService - ERROR -    - Alpha: ts_product(winsorize(ts_backfill((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500))/ts_std_dev((ts_regression(ts_zscore(cogs,500), ts_zscore(liabilities,500),500)),500), 120), std=4), 22), Decay: 0
2025-08-02 16:19:16,230 - SimulationService - WARNING - ❌ 任务 10 失败https://api.worldquantbrain.com/simulations/3aI75t9Gk4IhaYaBxRhYtky: {"children":["7rvEw6Hc4wVcmWcf3JfZrG","2UzRGJ3fF57M8CMkRDIBaMm","4m077Pfvj4DebqvN4qyNici","1bsPja1pf4mI9xc107s13v9i","jltODcz659gc3qAhzU1njx","KgnvRaRe4mDcfkhtkhA0FR","jPxfo4x04xlba79GUCGbML","1C3Hb34H04Dcafn19lU3K3qj","46fFak1bF53m9eFxWEKMRjM","li5ab1De5cx9VWCcxl8D9W"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:19:16,230 - SimulationService - INFO - 正在查询任务 10 的 10 个子任务详情...
2025-08-02 16:19:16,739 - SimulationService - INFO - 📄 任务 10 子任务 1/10 (ID: 7rvEw6Hc4wVcmWcf3JfZrG):
2025-08-02 16:19:16,739 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:17,660 - SimulationService - INFO - 📄 任务 10 子任务 2/10 (ID: 2UzRGJ3fF57M8CMkRDIBaMm):
2025-08-02 16:19:17,660 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:18,486 - SimulationService - INFO - 📄 任务 10 子任务 3/10 (ID: 4m077Pfvj4DebqvN4qyNici):
2025-08-02 16:19:18,486 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:18,486 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:19:19,401 - SimulationService - INFO - 📄 任务 10 子任务 4/10 (ID: 1bsPja1pf4mI9xc107s13v9i):
2025-08-02 16:19:19,402 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:19,402 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:19:20,328 - SimulationService - INFO - 📄 任务 10 子任务 5/10 (ID: jltODcz659gc3qAhzU1njx):
2025-08-02 16:19:20,329 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:20,329 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:19:21,348 - SimulationService - INFO - 📄 任务 10 子任务 6/10 (ID: KgnvRaRe4mDcfkhtkhA0FR):
2025-08-02 16:19:21,348 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:21,348 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:19:22,679 - SimulationService - INFO - 📄 任务 10 子任务 7/10 (ID: jPxfo4x04xlba79GUCGbML):
2025-08-02 16:19:22,680 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:19:22,680 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:19:24,410 - SimulationService - INFO - 📄 任务 10 子任务 8/10 (ID: 1C3Hb34H04Dcafn19lU3K3qj):
2025-08-02 16:19:24,412 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:25,342 - SimulationService - INFO - 📄 任务 10 子任务 9/10 (ID: 46fFak1bF53m9eFxWEKMRjM):
2025-08-02 16:19:25,343 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:26,265 - SimulationService - INFO - 📄 任务 10 子任务 10/10 (ID: li5ab1De5cx9VWCcxl8D9W):
2025-08-02 16:19:26,265 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:19:27,212 - SimulationService - INFO - 任务 12 提交成功
2025-08-02 16:20:02,070 - ReplicatedAlphaMachine - INFO - 开始清理资源
2025-08-02 16:20:02,074 - ReplicatedAlphaMachine - INFO - 等待异步数据库操作完成
2025-08-02 16:20:02,075 - AsyncDatabaseService - INFO - 所有异步数据库操作已完成
2025-08-02 16:20:02,075 - AsyncDatabaseService - INFO - 开始关闭异步数据库服务
2025-08-02 16:20:02,075 - AsyncDatabaseService - INFO - 线程池已关闭
2025-08-02 16:20:02,081 - AsyncDatabaseService - INFO - 所有线程数据库连接已关闭
2025-08-02 16:20:02,081 - AsyncDatabaseService - INFO - 异步数据库服务已关闭
2025-08-02 16:20:02,081 - ReplicatedAlphaMachine - INFO - 数据库连接已关闭
2025-08-02 16:20:02,081 - ReplicatedAlphaMachine - INFO - 资源清理完成
