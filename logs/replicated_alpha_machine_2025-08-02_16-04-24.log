2025-08-02 16:04:24,659 - ReplicatedAlphaMachine - INFO - 日志系统初始化完成，日志文件: logs/replicated_alpha_machine_2025-08-02_16-04-24.log
2025-08-02 16:04:24,659 - ReplicatedAlphaMachine - INFO - 加载配置文件
2025-08-02 16:04:24,660 - root - INFO - dataset_id: model26
2025-08-02 16:04:24,660 - root - INFO - region: ASI
2025-08-02 16:04:24,660 - root - INFO - universe: MINVOL1M
2025-08-02 16:04:24,660 - root - INFO - delay: 1
2025-08-02 16:04:24,660 - root - INFO - decay: 0
2025-08-02 16:04:24,660 - root - INFO - neutralization: SUBINDUSTRY
2025-08-02 16:04:24,660 - root - INFO - prefix: winsorize
2025-08-02 16:04:24,660 - root - INFO - coverage: 0.8
2025-08-02 16:04:24,660 - root - INFO - truncation: 0.08
2025-08-02 16:04:24,660 - root - INFO - instrument_type: EQUITY
2025-08-02 16:04:24,660 - root - INFO - pasteurization: ON
2025-08-02 16:04:24,660 - root - INFO - nan_handling: OFF
2025-08-02 16:04:24,660 - root - INFO - unit_handling: VERIFY
2025-08-02 16:04:24,660 - root - INFO - language: FASTEXPR
2025-08-02 16:04:24,660 - root - INFO - visualization: false
2025-08-02 16:04:24,660 - root - INFO - max_trade: ON
2025-08-02 16:04:24,660 - root - INFO - test_period: P0Y
2025-08-02 16:04:24,660 - root - INFO - events: open_events
2025-08-02 16:04:24,661 - root - INFO - alpha_num_filter: 2000
2025-08-02 16:04:24,661 - root - INFO - alpha_num_submit: 500
2025-08-02 16:04:24,661 - root - INFO - third_promote_num: 200
2025-08-02 16:04:24,661 - root - INFO - batch_size: 500
2025-08-02 16:04:24,661 - root - INFO - second_promote_sharp: 1.2
2025-08-02 16:04:24,661 - root - INFO - second_promote_fitness: 0.7
2025-08-02 16:04:24,661 - root - INFO - third_promote_sharp: 1.3
2025-08-02 16:04:24,661 - root - INFO - third_promote_fitness: 0.8
2025-08-02 16:04:24,661 - root - INFO - submit_sharp_th: 1.58
2025-08-02 16:04:24,661 - root - INFO - submit_fitness_th: 1.0
2025-08-02 16:04:24,661 - root - INFO - submit_margin_th: 1.0
2025-08-02 16:04:24,661 - root - INFO - is_simulate: True
2025-08-02 16:04:24,661 - root - INFO - is_second_promote: True
2025-08-02 16:04:24,661 - root - INFO - is_third_promote: True
2025-08-02 16:04:24,661 - root - INFO - is_prune: False
2025-08-02 16:04:24,661 - root - INFO - fields_file: filtered_shuffled_expressions.txt
2025-08-02 16:04:24,661 - root - INFO - is_filter_pnl: False
2025-08-02 16:04:24,661 - root - INFO - is_preprocess_alpha: True
2025-08-02 16:04:24,661 - ReplicatedAlphaMachine - INFO - 初始化数据库服务
2025-08-02 16:04:24,661 - root - INFO - 初始化数据库连接
2025-08-02 16:04:24,662 - root - INFO - 数据库连接成功
2025-08-02 16:04:24,664 - root - INFO - 数据库表创建/检查完成
2025-08-02 16:04:24,664 - root - INFO - check_status 字段已存在，跳过迁移
2025-08-02 16:04:24,664 - root - INFO - 数据库初始化完成
2025-08-02 16:04:24,664 - root - INFO - 创建异步数据库服务
2025-08-02 16:04:24,664 - AsyncDatabaseService - INFO - 异步数据库服务已初始化，线程池大小: 3
2025-08-02 16:04:24,664 - ReplicatedAlphaMachine - INFO - 初始化认证服务
2025-08-02 16:04:24,665 - ReplicatedAlphaMachine - INFO - 初始化业务服务
2025-08-02 16:04:24,665 - ReplicatedAlphaMachine - INFO - Alpha机器初始化完成
2025-08-02 16:04:24,665 - ReplicatedAlphaMachine - INFO - 开始Alpha生成和仿真流程
2025-08-02 16:04:24,665 - ReplicatedAlphaMachine - INFO - 开始生成Alpha表达式
2025-08-02 16:04:24,665 - AlphaService - INFO - 从文件 decoded_expressions (21).txt 创建Alpha
2025-08-02 16:04:24,666 - AlphaService - INFO - 从文件加载了 2000 个数据字段
2025-08-02 16:04:24,712 - AlphaFactory - INFO - 生成了 226000 个一阶Alpha表达式
2025-08-02 16:04:24,740 - AlphaService - INFO - 生成了 226000 个Alpha表达式
2025-08-02 16:04:24,742 - ReplicatedAlphaMachine - INFO - 生成了 226000 个Alpha，开始过滤已回测的Alpha
2025-08-02 16:04:25,647 - root - INFO - 已过滤掉 0 个已回测的alpha，剩余 226000 个新alpha待回测
2025-08-02 16:04:25,657 - ReplicatedAlphaMachine - INFO - 过滤后剩余 226000 个未回测的Alpha
2025-08-02 16:04:25,658 - ReplicatedAlphaMachine - INFO - 执行日期范围: 12-01 到 12-07
2025-08-02 16:04:25,658 - ReplicatedAlphaMachine - INFO - 开始批量仿真 226000 个Alpha
2025-08-02 16:04:25,658 - SimulationService - INFO - 开始批量仿真，批次大小: 500
2025-08-02 16:04:25,658 - SimulationService - INFO - 进行第 1/452 批仿真，数量: 500
2025-08-02 16:04:25,658 - SimulationService - INFO - 开始仿真 500 个Alpha
2025-08-02 16:04:25,658 - SimulationService - INFO - 准备仿真 50 个任务
2025-08-02 16:04:27,238 - root - INFO - 登录成功
2025-08-02 16:04:27,238 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-02 16:04:27,794 - SimulationService - INFO - 任务 0 提交成功
2025-08-02 16:04:28,238 - SimulationService - INFO - 任务 1 提交成功
2025-08-02 16:04:28,770 - SimulationService - INFO - 任务 2 提交成功
2025-08-02 16:04:29,240 - SimulationService - INFO - 任务 3 提交成功
2025-08-02 16:04:29,732 - SimulationService - INFO - 任务 4 提交成功
2025-08-02 16:04:30,388 - SimulationService - INFO - 任务 5 提交成功
2025-08-02 16:04:30,901 - SimulationService - INFO - 任务 6 提交成功
2025-08-02 16:04:31,514 - SimulationService - INFO - 任务 7 提交成功
2025-08-02 16:04:36,837 - SimulationService - ERROR - ❌ 任务 0 失败，包含的Alpha表达式：
2025-08-02 16:04:36,838 - SimulationService - ERROR -    - Alpha: winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), Decay: 0
2025-08-02 16:04:36,838 - SimulationService - ERROR -    - Alpha: reverse(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:04:36,838 - SimulationService - ERROR -    - Alpha: inverse(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:04:36,838 - SimulationService - ERROR -    - Alpha: rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:04:36,838 - SimulationService - ERROR -    - Alpha: zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:04:36,838 - SimulationService - ERROR -    - Alpha: quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:04:36,839 - SimulationService - ERROR -    - Alpha: normalize(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4)), Decay: 0
2025-08-02 16:04:36,839 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, constant=0), Decay: 0
2025-08-02 16:04:36,839 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, constant=0), Decay: 0
2025-08-02 16:04:36,839 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, constant=0), Decay: 0
2025-08-02 16:04:36,839 - SimulationService - WARNING - ❌ 任务 0 失败https://api.worldquantbrain.com/simulations/EoUGBo5gf9ntzkKR3QNz: {"children":["4hneyBdWT4hpba59HHfLiam","3UTRvMd9h4QA8Cmp2jNKrm8","Ne8HT5hK5hG8Y6Yq2IUBjM","4mMGmRgad50VccZs8dtPD3J","3izHLF9y553i9GWVg2sANxA","2iFmat8LA4O48BQLEY5BJRN","35x9sa6cT5fN9uePMPwbph2","2T2zM0fCY4oybQMZSSvAVoJ","3BEdOF3aZ5hn9g4emfQ4ULq","3GrCXi4mk4nPcqo5Nn54pbK"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:04:36,840 - SimulationService - INFO - 正在查询任务 0 的 10 个子任务详情...
2025-08-02 16:04:37,158 - SimulationService - INFO - 📄 任务 0 子任务 1/10 (ID: 4hneyBdWT4hpba59HHfLiam):
2025-08-02 16:04:37,159 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:37,159 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:37,992 - SimulationService - INFO - 📄 任务 0 子任务 2/10 (ID: 3UTRvMd9h4QA8Cmp2jNKrm8):
2025-08-02 16:04:37,993 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:37,993 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:38,914 - SimulationService - INFO - 📄 任务 0 子任务 3/10 (ID: Ne8HT5hK5hG8Y6Yq2IUBjM):
2025-08-02 16:04:38,915 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:38,915 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:39,727 - SimulationService - INFO - 📄 任务 0 子任务 4/10 (ID: 4mMGmRgad50VccZs8dtPD3J):
2025-08-02 16:04:39,727 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:39,727 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:40,833 - SimulationService - INFO - 📄 任务 0 子任务 5/10 (ID: 3izHLF9y553i9GWVg2sANxA):
2025-08-02 16:04:40,834 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:40,834 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:41,755 - SimulationService - INFO - 📄 任务 0 子任务 6/10 (ID: 2iFmat8LA4O48BQLEY5BJRN):
2025-08-02 16:04:41,756 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:41,756 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:42,598 - SimulationService - INFO - 📄 任务 0 子任务 7/10 (ID: 35x9sa6cT5fN9uePMPwbph2):
2025-08-02 16:04:42,599 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:42,599 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:43,494 - SimulationService - INFO - 📄 任务 0 子任务 8/10 (ID: 2T2zM0fCY4oybQMZSSvAVoJ):
2025-08-02 16:04:43,495 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:43,495 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:44,519 - SimulationService - INFO - 📄 任务 0 子任务 9/10 (ID: 3BEdOF3aZ5hn9g4emfQ4ULq):
2025-08-02 16:04:44,519 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:44,519 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:45,351 - SimulationService - INFO - 📄 任务 0 子任务 10/10 (ID: 3GrCXi4mk4nPcqo5Nn54pbK):
2025-08-02 16:04:45,351 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:45,351 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:46,388 - SimulationService - INFO - 任务 8 提交成功
2025-08-02 16:04:47,651 - SimulationService - ERROR - ❌ 任务 1 失败，包含的Alpha表达式：
2025-08-02 16:04:47,651 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, constant=0), Decay: 0
2025-08-02 16:04:47,652 - SimulationService - ERROR -    - Alpha: ts_rank(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, constant=0), Decay: 0
2025-08-02 16:04:47,652 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:04:47,652 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:04:47,652 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:04:47,652 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:04:47,652 - SimulationService - ERROR -    - Alpha: ts_zscore(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:04:47,652 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:04:47,653 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:04:47,653 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:04:47,653 - SimulationService - WARNING - ❌ 任务 1 失败https://api.worldquantbrain.com/simulations/lzSj11cS4Yjckc18D3oC4u7: {"children":["4klpXag8e4gt8LQL0H3weDL","3XgWCS5jJ4RNahu14nKVLMSm","LxRZy2Iy4tGbSAykGGlNsV","z42IZ6IV5fka7Wp11wBLMw","27Nv48c5s4UNbgp4a797HF1","2kidRS9Z45ipbXdjRjWMcG0","Cx4RHbdg4T58BZgtLL3VOY","1MxkZff4y4pr8KbeXiYPkqt","4jBaFf7ac56Zc7LNInLwDGX","4wHYGj4Dh4l4bR9rLELjquC"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:04:47,653 - SimulationService - INFO - 正在查询任务 1 的 10 个子任务详情...
2025-08-02 16:04:48,147 - SimulationService - INFO - 📄 任务 1 子任务 1/10 (ID: 4klpXag8e4gt8LQL0H3weDL):
2025-08-02 16:04:48,148 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:48,148 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:49,384 - SimulationService - INFO - 📄 任务 1 子任务 2/10 (ID: 3XgWCS5jJ4RNahu14nKVLMSm):
2025-08-02 16:04:49,385 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:49,385 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:50,271 - SimulationService - INFO - 📄 任务 1 子任务 3/10 (ID: LxRZy2Iy4tGbSAykGGlNsV):
2025-08-02 16:04:50,271 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:50,272 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:51,183 - SimulationService - INFO - 📄 任务 1 子任务 4/10 (ID: z42IZ6IV5fka7Wp11wBLMw):
2025-08-02 16:04:51,184 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:51,184 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:52,332 - SimulationService - INFO - 📄 任务 1 子任务 5/10 (ID: 27Nv48c5s4UNbgp4a797HF1):
2025-08-02 16:04:52,333 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:52,333 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:53,374 - SimulationService - INFO - 📄 任务 1 子任务 6/10 (ID: 2kidRS9Z45ipbXdjRjWMcG0):
2025-08-02 16:04:53,375 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:53,375 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:54,382 - SimulationService - INFO - 📄 任务 1 子任务 7/10 (ID: Cx4RHbdg4T58BZgtLL3VOY):
2025-08-02 16:04:54,382 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:54,382 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:55,586 - SimulationService - INFO - 📄 任务 1 子任务 8/10 (ID: 1MxkZff4y4pr8KbeXiYPkqt):
2025-08-02 16:04:55,587 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:55,587 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:56,603 - SimulationService - INFO - 📄 任务 1 子任务 9/10 (ID: 4jBaFf7ac56Zc7LNInLwDGX):
2025-08-02 16:04:56,604 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:56,604 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:57,748 - SimulationService - INFO - 📄 任务 1 子任务 10/10 (ID: 4wHYGj4Dh4l4bR9rLELjquC):
2025-08-02 16:04:57,748 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:57,748 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:04:58,849 - SimulationService - INFO - 任务 9 提交成功
2025-08-02 16:04:59,235 - SimulationService - ERROR - ❌ 任务 2 失败，包含的Alpha表达式：
2025-08-02 16:04:59,235 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:04:59,235 - SimulationService - ERROR -    - Alpha: ts_delta(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:04:59,236 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:04:59,236 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:04:59,236 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:04:59,236 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:04:59,236 - SimulationService - ERROR -    - Alpha: ts_sum(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:04:59,236 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:04:59,236 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:04:59,237 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:04:59,237 - SimulationService - WARNING - ❌ 任务 2 失败https://api.worldquantbrain.com/simulations/3YZ1KLg94UfbKl12i62HAqb: {"children":["2BraE4xz53Mblu1edFcIjQt","3zQpa95aC4FB8PP1fFSLtR1e","WeQtIaGM5d48zrq3H7Nz18","17Au1d3Ko5dN8MfoHe1QviN","N2WGpbzh4lLb3s13vr6VMwP","FZgUnPr5c9cpy16iB9Y6eb","eXoDW8Oo4iZcwRNOrpdHa6","2he6zh2O4EjcuB1gQ36ujOl","4DfHBGbrH4RDcB7NWmK6XB6","1mNENT8tR4sO9JUKLQ7nU7H"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:04:59,237 - SimulationService - INFO - 正在查询任务 2 的 10 个子任务详情...
2025-08-02 16:04:59,675 - SimulationService - INFO - 📄 任务 2 子任务 1/10 (ID: 2BraE4xz53Mblu1edFcIjQt):
2025-08-02 16:04:59,675 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:04:59,675 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:00,801 - SimulationService - INFO - 📄 任务 2 子任务 2/10 (ID: 3zQpa95aC4FB8PP1fFSLtR1e):
2025-08-02 16:05:00,801 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:00,801 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:02,132 - SimulationService - INFO - 📄 任务 2 子任务 3/10 (ID: WeQtIaGM5d48zrq3H7Nz18):
2025-08-02 16:05:02,132 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:02,132 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:02,991 - SimulationService - INFO - 📄 任务 2 子任务 4/10 (ID: 17Au1d3Ko5dN8MfoHe1QviN):
2025-08-02 16:05:02,992 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:02,992 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:03,819 - SimulationService - INFO - 📄 任务 2 子任务 5/10 (ID: N2WGpbzh4lLb3s13vr6VMwP):
2025-08-02 16:05:03,819 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:03,820 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:05,986 - SimulationService - INFO - 📄 任务 2 子任务 6/10 (ID: FZgUnPr5c9cpy16iB9Y6eb):
2025-08-02 16:05:05,987 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:05,987 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:06,841 - SimulationService - INFO - 📄 任务 2 子任务 7/10 (ID: eXoDW8Oo4iZcwRNOrpdHa6):
2025-08-02 16:05:06,842 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:06,842 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:07,842 - SimulationService - INFO - 📄 任务 2 子任务 8/10 (ID: 2he6zh2O4EjcuB1gQ36ujOl):
2025-08-02 16:05:07,842 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:07,842 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:08,770 - SimulationService - INFO - 📄 任务 2 子任务 9/10 (ID: 4DfHBGbrH4RDcB7NWmK6XB6):
2025-08-02 16:05:08,771 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:08,771 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:09,711 - SimulationService - INFO - 📄 任务 2 子任务 10/10 (ID: 1mNENT8tR4sO9JUKLQ7nU7H):
2025-08-02 16:05:09,712 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:09,712 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:11,451 - SimulationService - INFO - 任务 10 提交成功
2025-08-02 16:05:11,871 - SimulationService - ERROR - ❌ 任务 3 失败，包含的Alpha表达式：
2025-08-02 16:05:11,872 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:05:11,872 - SimulationService - ERROR -    - Alpha: ts_delay(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:05:11,872 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:05:11,873 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:05:11,873 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:05:11,873 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:05:11,873 - SimulationService - ERROR -    - Alpha: ts_std_dev(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:05:11,873 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:05:11,873 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:05:11,873 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:05:11,874 - SimulationService - WARNING - ❌ 任务 3 失败https://api.worldquantbrain.com/simulations/3LUkYzayc4kpaJ3Lr58it4t: {"children":["2i6am12pF4UGcBCwl1mGNiD","4jpiGYc7v4nbas3EZJOOuq2","2rNNmh1874rM9fqvoMcVo0X","2AxhjFep94O38NRDQvkbH7Q","nP00274s4z597zr20u7q4G","13AojzduF4s6bMGT31i0lGS","4jqCnowC4KRcFyLhFXK57d","1Mxa7O5y14oycbt99hxRBMU","4CdbKN2rC4Pt9jBj4sQKduZ","OqQgpeoz4nUaT1h0HT44OD"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:05:11,874 - SimulationService - INFO - 正在查询任务 3 的 10 个子任务详情...
2025-08-02 16:05:12,270 - SimulationService - INFO - 📄 任务 3 子任务 1/10 (ID: 2i6am12pF4UGcBCwl1mGNiD):
2025-08-02 16:05:12,271 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:12,271 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:13,086 - SimulationService - INFO - 📄 任务 3 子任务 2/10 (ID: 4jpiGYc7v4nbas3EZJOOuq2):
2025-08-02 16:05:13,086 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:13,086 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:14,011 - SimulationService - INFO - 📄 任务 3 子任务 3/10 (ID: 2rNNmh1874rM9fqvoMcVo0X):
2025-08-02 16:05:14,011 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:14,012 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:14,932 - SimulationService - INFO - 📄 任务 3 子任务 4/10 (ID: 2AxhjFep94O38NRDQvkbH7Q):
2025-08-02 16:05:14,933 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:14,933 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:15,855 - SimulationService - INFO - 📄 任务 3 子任务 5/10 (ID: nP00274s4z597zr20u7q4G):
2025-08-02 16:05:15,856 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:15,856 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:16,775 - SimulationService - INFO - 📄 任务 3 子任务 6/10 (ID: 13AojzduF4s6bMGT31i0lGS):
2025-08-02 16:05:16,776 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:16,776 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:17,696 - SimulationService - INFO - 📄 任务 3 子任务 7/10 (ID: 4jqCnowC4KRcFyLhFXK57d):
2025-08-02 16:05:17,697 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:17,697 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:18,531 - SimulationService - INFO - 📄 任务 3 子任务 8/10 (ID: 1Mxa7O5y14oycbt99hxRBMU):
2025-08-02 16:05:18,531 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:18,531 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:19,438 - SimulationService - INFO - 📄 任务 3 子任务 9/10 (ID: 4CdbKN2rC4Pt9jBj4sQKduZ):
2025-08-02 16:05:19,439 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:19,439 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:20,373 - SimulationService - INFO - 📄 任务 3 子任务 10/10 (ID: OqQgpeoz4nUaT1h0HT44OD):
2025-08-02 16:05:20,374 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:20,374 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:22,010 - SimulationService - INFO - 任务 11 提交成功
2025-08-02 16:05:22,631 - SimulationService - ERROR - ❌ 任务 4 失败，包含的Alpha表达式：
2025-08-02 16:05:22,632 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:05:22,632 - SimulationService - ERROR -    - Alpha: ts_mean(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:05:22,633 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:05:22,633 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:05:22,633 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:05:22,633 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:05:22,633 - SimulationService - ERROR -    - Alpha: ts_arg_min(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:05:22,633 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:05:22,633 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:05:22,633 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:05:22,633 - SimulationService - WARNING - ❌ 任务 4 失败https://api.worldquantbrain.com/simulations/3EGdIT4O54JbbjNzGoc8Jcl: {"children":["1LFzfiaBy4B79A41btJmHfhf","1NjxF57g74iJbPv1dnbewgN0","leTjh7rm4NMb5Xro6VB0Gx","23x0Zr50h4nrauARaqSPRRE","2LdHQL6ez57jbc9QJOVmRr1","pkKTDeCA4t7aek1b0BRj8Ph","2olXFmaVt4u8cD6uFnVlm3o","2GC2aR6VA4nCbmC7BLL74OO","3kEMIsg8v4zNb9c15LNaNavd","vdnc9bpq4jI9HrfvrZ8bpP"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:05:22,633 - SimulationService - INFO - 正在查询任务 4 的 10 个子任务详情...
2025-08-02 16:05:22,989 - SimulationService - INFO - 📄 任务 4 子任务 1/10 (ID: 1LFzfiaBy4B79A41btJmHfhf):
2025-08-02 16:05:22,990 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:22,990 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:23,862 - SimulationService - INFO - 📄 任务 4 子任务 2/10 (ID: 1NjxF57g74iJbPv1dnbewgN0):
2025-08-02 16:05:23,863 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:23,863 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:24,692 - SimulationService - INFO - 📄 任务 4 子任务 3/10 (ID: leTjh7rm4NMb5Xro6VB0Gx):
2025-08-02 16:05:24,692 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:24,692 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:25,549 - SimulationService - INFO - 📄 任务 4 子任务 4/10 (ID: 23x0Zr50h4nrauARaqSPRRE):
2025-08-02 16:05:25,550 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:25,550 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:26,401 - SimulationService - INFO - 📄 任务 4 子任务 5/10 (ID: 2LdHQL6ez57jbc9QJOVmRr1):
2025-08-02 16:05:26,402 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:26,402 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:27,334 - SimulationService - INFO - 📄 任务 4 子任务 6/10 (ID: pkKTDeCA4t7aek1b0BRj8Ph):
2025-08-02 16:05:27,335 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:27,335 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:28,287 - SimulationService - INFO - 📄 任务 4 子任务 7/10 (ID: 2olXFmaVt4u8cD6uFnVlm3o):
2025-08-02 16:05:28,288 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:28,288 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:29,678 - SimulationService - INFO - 📄 任务 4 子任务 8/10 (ID: 2GC2aR6VA4nCbmC7BLL74OO):
2025-08-02 16:05:29,678 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:29,679 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:30,804 - SimulationService - INFO - 📄 任务 4 子任务 9/10 (ID: 3kEMIsg8v4zNb9c15LNaNavd):
2025-08-02 16:05:30,804 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:30,804 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:31,828 - SimulationService - INFO - 📄 任务 4 子任务 10/10 (ID: vdnc9bpq4jI9HrfvrZ8bpP):
2025-08-02 16:05:31,828 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:31,829 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:32,923 - SimulationService - INFO - 任务 12 提交成功
2025-08-02 16:05:33,248 - SimulationService - ERROR - ❌ 任务 5 失败，包含的Alpha表达式：
2025-08-02 16:05:33,249 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:05:33,249 - SimulationService - ERROR -    - Alpha: ts_arg_max(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:05:33,249 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, constant=0), Decay: 0
2025-08-02 16:05:33,249 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, constant=0), Decay: 0
2025-08-02 16:05:33,249 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, constant=0), Decay: 0
2025-08-02 16:05:33,249 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, constant=0), Decay: 0
2025-08-02 16:05:33,249 - SimulationService - ERROR -    - Alpha: ts_scale(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, constant=0), Decay: 0
2025-08-02 16:05:33,249 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, driver='gaussian'), Decay: 0
2025-08-02 16:05:33,250 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, driver='gaussian'), Decay: 0
2025-08-02 16:05:33,250 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, driver='gaussian'), Decay: 0
2025-08-02 16:05:33,250 - SimulationService - WARNING - ❌ 任务 5 失败https://api.worldquantbrain.com/simulations/1JpeeQ5WK4VWbzgoDnDqWY3: {"children":["oiIgFfyj57ObeZUlAEVk3R","RPEhs8Im4XQcsHLwqoSbU3","21oe2kePI4M49dZ18gIQvMIi","1L91sWg0c4SYctH9jYk2ScQ","4gGDF37ap4U296MFZolgFu1","1Qqmey6ts5229qI40aA1xdX","1V8tkI5qq4Pp9ym11HiOZznf","1WTNOy3bH5aKazK11mPtcu6G","4nlIzM18S4tzbx8S5AixzFR","26HXJR51Q4vW8PxawzCueI1"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:05:33,250 - SimulationService - INFO - 正在查询任务 5 的 10 个子任务详情...
2025-08-02 16:05:33,674 - SimulationService - INFO - 📄 任务 5 子任务 1/10 (ID: oiIgFfyj57ObeZUlAEVk3R):
2025-08-02 16:05:33,674 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:33,675 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:34,591 - SimulationService - INFO - 📄 任务 5 子任务 2/10 (ID: RPEhs8Im4XQcsHLwqoSbU3):
2025-08-02 16:05:34,592 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:34,593 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:35,742 - SimulationService - INFO - 📄 任务 5 子任务 3/10 (ID: 21oe2kePI4M49dZ18gIQvMIi):
2025-08-02 16:05:35,742 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:35,742 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:36,742 - SimulationService - INFO - 📄 任务 5 子任务 4/10 (ID: 1L91sWg0c4SYctH9jYk2ScQ):
2025-08-02 16:05:36,743 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:36,743 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:37,664 - SimulationService - INFO - 📄 任务 5 子任务 5/10 (ID: 4gGDF37ap4U296MFZolgFu1):
2025-08-02 16:05:37,665 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:37,665 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:38,586 - SimulationService - INFO - 📄 任务 5 子任务 6/10 (ID: 1Qqmey6ts5229qI40aA1xdX):
2025-08-02 16:05:38,587 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:38,587 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:39,608 - SimulationService - INFO - 📄 任务 5 子任务 7/10 (ID: 1V8tkI5qq4Pp9ym11HiOZznf):
2025-08-02 16:05:39,608 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:39,609 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:40,735 - SimulationService - INFO - 📄 任务 5 子任务 8/10 (ID: 1WTNOy3bH5aKazK11mPtcu6G):
2025-08-02 16:05:40,735 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:40,735 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:41,657 - SimulationService - INFO - 📄 任务 5 子任务 9/10 (ID: 4nlIzM18S4tzbx8S5AixzFR):
2025-08-02 16:05:41,658 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:41,658 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:42,496 - SimulationService - INFO - 📄 任务 5 子任务 10/10 (ID: 26HXJR51Q4vW8PxawzCueI1):
2025-08-02 16:05:42,497 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:42,497 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:44,214 - SimulationService - INFO - 任务 13 提交成功
2025-08-02 16:05:44,563 - SimulationService - ERROR - ❌ 任务 6 失败，包含的Alpha表达式：
2025-08-02 16:05:44,563 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, driver='gaussian'), Decay: 0
2025-08-02 16:05:44,563 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, driver='gaussian'), Decay: 0
2025-08-02 16:05:44,563 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:05:44,563 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:05:44,563 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:05:44,563 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:05:44,564 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:05:44,564 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:05:44,564 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:05:44,564 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:05:44,564 - SimulationService - WARNING - ❌ 任务 6 失败https://api.worldquantbrain.com/simulations/3qR4kb1kP4F0cH81eI0YPc2A: {"children":["1TIMr4fEw4ZCcbjIs8zXCTB","2vfArQ2Wl4qBbQ6cYaqWF3a","1BoGKU9YU4Iy9RKopR9kcj3","2TRy93fJZ5g4cjZ1905OInNz","80RxX8a4LxcJW16yt9Q6Qy","4udYw97xv4YHaWnuFjpYPuz","I7ZtvdEs54PbaIDQ1YZLey","275Brk9mm59w8OiUSmQQyxw","28dX7Xglc4WTbFhpek0ASPK","3TKgkf4wz5bgcGY5lN6DQEG"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:05:44,564 - SimulationService - INFO - 正在查询任务 6 的 10 个子任务详情...
2025-08-02 16:05:44,934 - SimulationService - INFO - 📄 任务 6 子任务 1/10 (ID: 1TIMr4fEw4ZCcbjIs8zXCTB):
2025-08-02 16:05:44,934 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:44,934 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:46,072 - SimulationService - INFO - 📄 任务 6 子任务 2/10 (ID: 2vfArQ2Wl4qBbQ6cYaqWF3a):
2025-08-02 16:05:46,073 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:46,073 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:46,982 - SimulationService - INFO - 📄 任务 6 子任务 3/10 (ID: 1BoGKU9YU4Iy9RKopR9kcj3):
2025-08-02 16:05:46,982 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:46,982 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:47,807 - SimulationService - INFO - 📄 任务 6 子任务 4/10 (ID: 2TRy93fJZ5g4cjZ1905OInNz):
2025-08-02 16:05:47,808 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:47,808 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:48,722 - SimulationService - INFO - 📄 任务 6 子任务 5/10 (ID: 80RxX8a4LxcJW16yt9Q6Qy):
2025-08-02 16:05:48,722 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:48,722 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:49,654 - SimulationService - INFO - 📄 任务 6 子任务 6/10 (ID: 4udYw97xv4YHaWnuFjpYPuz):
2025-08-02 16:05:49,654 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:49,654 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:50,671 - SimulationService - INFO - 📄 任务 6 子任务 7/10 (ID: I7ZtvdEs54PbaIDQ1YZLey):
2025-08-02 16:05:50,672 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:50,672 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:51,888 - SimulationService - INFO - 📄 任务 6 子任务 8/10 (ID: 275Brk9mm59w8OiUSmQQyxw):
2025-08-02 16:05:51,889 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:51,889 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:52,817 - SimulationService - INFO - 📄 任务 6 子任务 9/10 (ID: 28dX7Xglc4WTbFhpek0ASPK):
2025-08-02 16:05:52,817 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:52,817 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:53,638 - SimulationService - INFO - 📄 任务 6 子任务 10/10 (ID: 3TKgkf4wz5bgcGY5lN6DQEG):
2025-08-02 16:05:53,639 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:53,639 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:54,765 - SimulationService - INFO - 任务 14 提交成功
2025-08-02 16:05:55,136 - SimulationService - ERROR - ❌ 任务 7 失败，包含的Alpha表达式：
2025-08-02 16:05:55,136 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:05:55,136 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:05:55,136 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, f=0.5), Decay: 0
2025-08-02 16:05:55,137 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, f=0.5), Decay: 0
2025-08-02 16:05:55,137 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, f=0.5), Decay: 0
2025-08-02 16:05:55,137 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, f=0.5), Decay: 0
2025-08-02 16:05:55,137 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, f=0.5), Decay: 0
2025-08-02 16:05:55,137 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, f=2), Decay: 0
2025-08-02 16:05:55,137 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, f=2), Decay: 0
2025-08-02 16:05:55,137 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, f=2), Decay: 0
2025-08-02 16:05:55,137 - SimulationService - WARNING - ❌ 任务 7 失败https://api.worldquantbrain.com/simulations/3Qbphudt156ka4mXr1YRjzr: {"children":["3rrJy4bXx4Pxc1QIwuIyD77","3n5g7Q54g4h6bLx1dFEvzz9w","3Qfl3o3Fr4pk8KR7Bntj8sQ","3GpAofe5p4pX8CpgY4bXihU","3bsSJ5qg4NF91gdVkrnQzJ","1SN8P5c9K528bMy7tINCQvW","2nQlwgeym4gHcLXNtU3UZUO","3YGXfNes94APbzbTmySJzsI","1QgYK4bPD4uUc6CkiUWQEvQ","3rDzWJ33N4gZbMmOhxE6nTZ"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:05:55,138 - SimulationService - INFO - 正在查询任务 7 的 10 个子任务详情...
2025-08-02 16:05:55,491 - SimulationService - INFO - 📄 任务 7 子任务 1/10 (ID: 3rrJy4bXx4Pxc1QIwuIyD77):
2025-08-02 16:05:55,491 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:55,491 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:56,402 - SimulationService - INFO - 📄 任务 7 子任务 2/10 (ID: 3n5g7Q54g4h6bLx1dFEvzz9w):
2025-08-02 16:05:56,402 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:56,403 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:57,332 - SimulationService - INFO - 📄 任务 7 子任务 3/10 (ID: 3Qfl3o3Fr4pk8KR7Bntj8sQ):
2025-08-02 16:05:57,332 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:57,332 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:58,157 - SimulationService - INFO - 📄 任务 7 子任务 4/10 (ID: 3GpAofe5p4pX8CpgY4bXihU):
2025-08-02 16:05:58,157 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:58,158 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:59,031 - SimulationService - INFO - 📄 任务 7 子任务 5/10 (ID: 3bsSJ5qg4NF91gdVkrnQzJ):
2025-08-02 16:05:59,031 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:59,031 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:05:59,871 - SimulationService - INFO - 📄 任务 7 子任务 6/10 (ID: 1SN8P5c9K528bMy7tINCQvW):
2025-08-02 16:05:59,871 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:05:59,871 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:00,721 - SimulationService - INFO - 📄 任务 7 子任务 7/10 (ID: 2nQlwgeym4gHcLXNtU3UZUO):
2025-08-02 16:06:00,722 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:00,722 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:01,591 - SimulationService - INFO - 📄 任务 7 子任务 8/10 (ID: 3YGXfNes94APbzbTmySJzsI):
2025-08-02 16:06:01,592 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:01,592 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:02,471 - SimulationService - INFO - 📄 任务 7 子任务 9/10 (ID: 1QgYK4bPD4uUc6CkiUWQEvQ):
2025-08-02 16:06:02,472 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:02,472 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:03,738 - SimulationService - INFO - 📄 任务 7 子任务 10/10 (ID: 3rDzWJ33N4gZbMmOhxE6nTZ):
2025-08-02 16:06:03,738 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:03,738 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:05,008 - SimulationService - INFO - 任务 15 提交成功
2025-08-02 16:06:05,417 - SimulationService - ERROR - ❌ 任务 8 失败，包含的Alpha表达式：
2025-08-02 16:06:05,417 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, f=2), Decay: 0
2025-08-02 16:06:05,417 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, f=2), Decay: 0
2025-08-02 16:06:05,417 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:06:05,418 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:06:05,418 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:06:05,418 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:06:05,418 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:06:05,418 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5), Decay: 0
2025-08-02 16:06:05,418 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22), Decay: 0
2025-08-02 16:06:05,418 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66), Decay: 0
2025-08-02 16:06:05,419 - SimulationService - WARNING - ❌ 任务 8 失败https://api.worldquantbrain.com/simulations/144uzz47Z4t4c6pFuCeiUNF: {"children":["1ESujk7EL4YO9rtJSFpnqZu","1ObhdcdK44SUbAG8or1HW5g","2oHzf76oS4xbboHECHzDWmh","29fd6S44L4W1cAv129aXBLJY","2o99r0ekz4H08xE10EpDWvMD","mSS7xa064y39FGxiymzI43","2n2qa2fsN4vicfUPhVZfX7h","jdc4jetD4Wwb9aA4PWETjd","3sBXry2JM55BbiT75lOgIPR","27YJrl8c54NT9AG3Entul4q"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:06:05,419 - SimulationService - INFO - 正在查询任务 8 的 10 个子任务详情...
2025-08-02 16:06:05,731 - SimulationService - INFO - 📄 任务 8 子任务 1/10 (ID: 1ESujk7EL4YO9rtJSFpnqZu):
2025-08-02 16:06:05,731 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:05,731 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:06,654 - SimulationService - INFO - 📄 任务 8 子任务 2/10 (ID: 1ObhdcdK44SUbAG8or1HW5g):
2025-08-02 16:06:06,655 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:06,655 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:07,668 - SimulationService - INFO - 📄 任务 8 子任务 3/10 (ID: 2oHzf76oS4xbboHECHzDWmh):
2025-08-02 16:06:07,669 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:07,669 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:08,895 - SimulationService - INFO - 📄 任务 8 子任务 4/10 (ID: 29fd6S44L4W1cAv129aXBLJY):
2025-08-02 16:06:08,895 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:08,895 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:09,817 - SimulationService - INFO - 📄 任务 8 子任务 5/10 (ID: 2o99r0ekz4H08xE10EpDWvMD):
2025-08-02 16:06:09,817 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:09,817 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:10,674 - SimulationService - INFO - 📄 任务 8 子任务 6/10 (ID: mSS7xa064y39FGxiymzI43):
2025-08-02 16:06:10,675 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:10,679 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:11,577 - SimulationService - INFO - 📄 任务 8 子任务 7/10 (ID: 2n2qa2fsN4vicfUPhVZfX7h):
2025-08-02 16:06:11,578 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:11,579 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:12,479 - SimulationService - INFO - 📄 任务 8 子任务 8/10 (ID: jdc4jetD4Wwb9aA4PWETjd):
2025-08-02 16:06:12,479 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:12,479 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:13,309 - SimulationService - INFO - 📄 任务 8 子任务 9/10 (ID: 3sBXry2JM55BbiT75lOgIPR):
2025-08-02 16:06:13,309 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:13,309 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:14,165 - SimulationService - INFO - 📄 任务 8 子任务 10/10 (ID: 27YJrl8c54NT9AG3Entul4q):
2025-08-02 16:06:14,166 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:14,166 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:15,246 - SimulationService - INFO - 任务 16 提交成功
2025-08-02 16:06:15,655 - SimulationService - ERROR - ❌ 任务 9 失败，包含的Alpha表达式：
2025-08-02 16:06:15,655 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120), Decay: 0
2025-08-02 16:06:15,655 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240), Decay: 0
2025-08-02 16:06:15,656 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, dense=false), Decay: 0
2025-08-02 16:06:15,656 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, dense=false), Decay: 0
2025-08-02 16:06:15,656 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, dense=false), Decay: 0
2025-08-02 16:06:15,656 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 120, dense=false), Decay: 0
2025-08-02 16:06:15,656 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 240, dense=false), Decay: 0
2025-08-02 16:06:15,657 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 5, lag=0, rettype=0), Decay: 0
2025-08-02 16:06:15,657 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 22, lag=0, rettype=0), Decay: 0
2025-08-02 16:06:15,657 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), winsorize(ts_backfill(residual = ts_regression (ts_zscore(fnd6_cptmfmq_atq,500), ts_zscore(fnd6_esopct,500),500);residual/ts_std_dev(residual,500), 120), std=4), 66, lag=0, rettype=0), Decay: 0
2025-08-02 16:06:15,657 - SimulationService - WARNING - ❌ 任务 9 失败https://api.worldquantbrain.com/simulations/3H4ZTF33a4t6cmvLGJOi3yO: {"children":["WuUohdD84kWcjx7bXcmySN","1l9w8CdfR5dnbrOVe49EjzI","1omdBJfmq4yOahHinYC5k18","1wFCIzetP55PclPhwvu8Gnw","oZsN58UU4vic5pYLjkSxbx","2yiSV85ie4kj8xJ1c9NZKMKL","1YhCZEfbK4xv9NqlaHPfRJm","a90156Z4512ajr17xRPPRMj","1nWPfP13H59M8V3hDptaW31","2J09V69bG4Bwam8j6c2GRrM"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:06:15,657 - SimulationService - INFO - 正在查询任务 9 的 10 个子任务详情...
2025-08-02 16:06:16,473 - SimulationService - INFO - 📄 任务 9 子任务 1/10 (ID: WuUohdD84kWcjx7bXcmySN):
2025-08-02 16:06:16,473 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:16,473 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:17,394 - SimulationService - INFO - 📄 任务 9 子任务 2/10 (ID: 1l9w8CdfR5dnbrOVe49EjzI):
2025-08-02 16:06:17,395 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:17,395 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:18,231 - SimulationService - INFO - 📄 任务 9 子任务 3/10 (ID: 1omdBJfmq4yOahHinYC5k18):
2025-08-02 16:06:18,232 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:18,232 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:19,139 - SimulationService - INFO - 📄 任务 9 子任务 4/10 (ID: 1wFCIzetP55PclPhwvu8Gnw):
2025-08-02 16:06:19,140 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:19,140 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
2025-08-02 16:06:20,159 - SimulationService - INFO - 📄 任务 9 子任务 5/10 (ID: oZsN58UU4vic5pYLjkSxbx):
2025-08-02 16:06:20,160 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:06:20,160 - SimulationService - ERROR -    错误信息: Unexpected character '(' near "egression (ts_zscore"
