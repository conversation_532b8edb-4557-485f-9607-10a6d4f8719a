2025-08-02 15:55:25,277 - ReplicatedAlphaMachine - INFO - 日志系统初始化完成，日志文件: logs/replicated_alpha_machine_2025-08-02_15-55-25.log
2025-08-02 15:55:25,277 - ReplicatedAlphaMachine - INFO - 加载配置文件
2025-08-02 15:55:25,278 - root - INFO - dataset_id: model26
2025-08-02 15:55:25,278 - root - INFO - region: ASI
2025-08-02 15:55:25,278 - root - INFO - universe: MINVOL1M
2025-08-02 15:55:25,278 - root - INFO - delay: 1
2025-08-02 15:55:25,278 - root - INFO - decay: 0
2025-08-02 15:55:25,278 - root - INFO - neutralization: SUBINDUSTRY
2025-08-02 15:55:25,278 - root - INFO - prefix: winsorize
2025-08-02 15:55:25,278 - root - INFO - coverage: 0.8
2025-08-02 15:55:25,278 - root - INFO - truncation: 0.08
2025-08-02 15:55:25,278 - root - INFO - instrument_type: EQUITY
2025-08-02 15:55:25,278 - root - INFO - pasteurization: ON
2025-08-02 15:55:25,278 - root - INFO - nan_handling: OFF
2025-08-02 15:55:25,278 - root - INFO - unit_handling: VERIFY
2025-08-02 15:55:25,278 - root - INFO - language: FASTEXPR
2025-08-02 15:55:25,278 - root - INFO - visualization: false
2025-08-02 15:55:25,278 - root - INFO - max_trade: ON
2025-08-02 15:55:25,278 - root - INFO - test_period: P0Y
2025-08-02 15:55:25,278 - root - INFO - events: open_events
2025-08-02 15:55:25,279 - root - INFO - alpha_num_filter: 2000
2025-08-02 15:55:25,279 - root - INFO - alpha_num_submit: 500
2025-08-02 15:55:25,279 - root - INFO - third_promote_num: 200
2025-08-02 15:55:25,279 - root - INFO - batch_size: 500
2025-08-02 15:55:25,279 - root - INFO - second_promote_sharp: 1.2
2025-08-02 15:55:25,279 - root - INFO - second_promote_fitness: 0.7
2025-08-02 15:55:25,279 - root - INFO - third_promote_sharp: 1.3
2025-08-02 15:55:25,279 - root - INFO - third_promote_fitness: 0.8
2025-08-02 15:55:25,279 - root - INFO - submit_sharp_th: 1.58
2025-08-02 15:55:25,279 - root - INFO - submit_fitness_th: 1.0
2025-08-02 15:55:25,279 - root - INFO - submit_margin_th: 1.0
2025-08-02 15:55:25,279 - root - INFO - is_simulate: True
2025-08-02 15:55:25,279 - root - INFO - is_second_promote: True
2025-08-02 15:55:25,279 - root - INFO - is_third_promote: True
2025-08-02 15:55:25,279 - root - INFO - is_prune: False
2025-08-02 15:55:25,279 - root - INFO - fields_file: filtered_shuffled_expressions.txt
2025-08-02 15:55:25,279 - root - INFO - is_filter_pnl: False
2025-08-02 15:55:25,279 - root - INFO - is_preprocess_alpha: True
2025-08-02 15:55:25,279 - ReplicatedAlphaMachine - INFO - 初始化数据库服务
2025-08-02 15:55:25,279 - root - INFO - 初始化数据库连接
2025-08-02 15:55:25,281 - root - INFO - 数据库连接成功
2025-08-02 15:55:25,283 - root - INFO - 数据库表创建/检查完成
2025-08-02 15:55:25,283 - root - INFO - check_status 字段已存在，跳过迁移
2025-08-02 15:55:25,283 - root - INFO - 数据库初始化完成
2025-08-02 15:55:25,283 - root - INFO - 创建异步数据库服务
2025-08-02 15:55:25,283 - AsyncDatabaseService - INFO - 异步数据库服务已初始化，线程池大小: 3
2025-08-02 15:55:25,283 - ReplicatedAlphaMachine - INFO - 初始化认证服务
2025-08-02 15:55:25,283 - ReplicatedAlphaMachine - INFO - 初始化业务服务
2025-08-02 15:55:25,284 - ReplicatedAlphaMachine - INFO - Alpha机器初始化完成
2025-08-02 15:55:25,284 - ReplicatedAlphaMachine - INFO - 开始Alpha生成和仿真流程
2025-08-02 15:55:25,284 - ReplicatedAlphaMachine - INFO - 开始生成Alpha表达式
2025-08-02 15:55:25,284 - AlphaService - INFO - 从文件 valid_test_factors.txt 创建Alpha
2025-08-02 15:55:25,284 - AlphaService - INFO - 从文件加载了 6 个数据字段
2025-08-02 15:55:25,284 - AlphaFactory - INFO - 生成了 678 个一阶Alpha表达式
2025-08-02 15:55:25,284 - AlphaService - INFO - 生成了 678 个Alpha表达式
2025-08-02 15:55:25,284 - ReplicatedAlphaMachine - INFO - 生成了 678 个Alpha，开始过滤已回测的Alpha
2025-08-02 15:55:25,288 - root - INFO - 已过滤掉 0 个已回测的alpha，剩余 678 个新alpha待回测
2025-08-02 15:55:25,288 - ReplicatedAlphaMachine - INFO - 过滤后剩余 678 个未回测的Alpha
2025-08-02 15:55:25,288 - ReplicatedAlphaMachine - INFO - 执行日期范围: 12-01 到 12-07
2025-08-02 15:55:25,288 - ReplicatedAlphaMachine - INFO - 开始批量仿真 678 个Alpha
2025-08-02 15:55:25,288 - SimulationService - INFO - 开始批量仿真，批次大小: 500
2025-08-02 15:55:25,288 - SimulationService - INFO - 进行第 1/2 批仿真，数量: 500
2025-08-02 15:55:25,288 - SimulationService - INFO - 开始仿真 500 个Alpha
2025-08-02 15:55:25,288 - SimulationService - INFO - 准备仿真 50 个任务
2025-08-02 15:55:26,770 - root - INFO - 登录成功
2025-08-02 15:55:26,771 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-02 15:55:27,229 - SimulationService - INFO - 任务 0 提交成功
2025-08-02 15:55:28,499 - SimulationService - INFO - 任务 1 提交成功
2025-08-02 15:55:28,952 - SimulationService - INFO - 任务 2 提交成功
2025-08-02 15:55:30,032 - SimulationService - INFO - 任务 3 提交成功
2025-08-02 15:55:30,459 - SimulationService - WARNING - 触发速率限制，等待 30 秒
2025-08-02 15:56:00,483 - SimulationService - ERROR - 请求失败: POST https://api.worldquantbrain.com/simulations, 错误: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
2025-08-02 15:56:00,483 - SimulationService - ERROR - 任务 4 提交失败 (重试 1/3): 请求失败，响应为空
2025-08-02 15:57:01,986 - SimulationService - INFO - 任务 4 提交成功
2025-08-02 15:57:02,858 - SimulationService - WARNING - 触发速率限制，等待 30 秒
2025-08-02 15:57:34,380 - SimulationService - INFO - 任务 5 提交成功
2025-08-02 15:57:34,750 - SimulationService - WARNING - 触发速率限制，等待 30 秒
2025-08-02 15:58:06,191 - SimulationService - WARNING - 触发速率限制，等待 30 秒
2025-08-02 15:58:37,730 - SimulationService - INFO - 任务 6 提交成功
2025-08-02 15:58:38,752 - SimulationService - WARNING - 触发速率限制，等待 30 秒
2025-08-02 15:59:10,220 - SimulationService - INFO - 任务 7 提交成功
2025-08-02 15:59:15,723 - SimulationService - INFO - ✅ 任务 0 完成，耗时 227s
2025-08-02 15:59:16,232 - SimulationService - INFO - 任务 8 提交成功
2025-08-02 15:59:16,642 - SimulationService - INFO - ✅ 任务 1 完成，耗时 227s
2025-08-02 15:59:17,080 - SimulationService - INFO - 任务 9 提交成功
2025-08-02 15:59:17,393 - SimulationService - INFO - ✅ 任务 2 完成，耗时 228s
2025-08-02 15:59:17,826 - SimulationService - INFO - 任务 10 提交成功
2025-08-02 15:59:19,304 - SimulationService - ERROR - ❌ 任务 6 失败，包含的Alpha表达式：
2025-08-02 15:59:19,304 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 120, driver='gaussian'), Decay: 0
2025-08-02 15:59:19,304 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 240, driver='gaussian'), Decay: 0
2025-08-02 15:59:19,304 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 15:59:19,305 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 15:59:19,305 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 66), Decay: 0
2025-08-02 15:59:19,305 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 120), Decay: 0
2025-08-02 15:59:19,305 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 240), Decay: 0
2025-08-02 15:59:19,305 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 15:59:19,305 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 15:59:19,305 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 66), Decay: 0
2025-08-02 15:59:19,305 - SimulationService - WARNING - ❌ 任务 6 失败https://api.worldquantbrain.com/simulations/9b1FlbIA5aU8NRA3YQkx77: {"children":["1smPUC5X84rw9eFIa6hky8","3n0xZdeiP4mF9GyjuhbC36O","m6BTCfr34LE9uh19kAbJnBA","3dt4Ca1o25379ETezPhOzAP","12yva1U75kj9iSXkhSfX1K","2rjSVNgnq4KS8BC5BeoCobg","3Z9yIf4hs4l6bLs18FuAA0S9","1oFP7J8pn56sb34ERstWkSv","4wFXTLbhz4rAagT10l3K5Tm4","1TqjSW1YU4Z88WVoRRAKYVH"],"type":"REGULAR","status":"ERROR"}
2025-08-02 15:59:19,305 - SimulationService - INFO - 正在查询任务 6 的 10 个子任务详情...
2025-08-02 15:59:19,713 - SimulationService - INFO - 📄 任务 6 子任务 1/10 (ID: 1smPUC5X84rw9eFIa6hky8):
2025-08-02 15:59:19,714 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 15:59:20,612 - SimulationService - INFO - 📄 任务 6 子任务 2/10 (ID: 3n0xZdeiP4mF9GyjuhbC36O):
2025-08-02 15:59:20,613 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 15:59:21,571 - SimulationService - INFO - 📄 任务 6 子任务 3/10 (ID: m6BTCfr34LE9uh19kAbJnBA):
2025-08-02 15:59:21,572 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:21,572 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 15:59:22,581 - SimulationService - INFO - 📄 任务 6 子任务 4/10 (ID: 3dt4Ca1o25379ETezPhOzAP):
2025-08-02 15:59:22,581 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:22,581 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 15:59:23,382 - SimulationService - INFO - 📄 任务 6 子任务 5/10 (ID: 12yva1U75kj9iSXkhSfX1K):
2025-08-02 15:59:23,383 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:23,383 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 15:59:24,208 - SimulationService - INFO - 📄 任务 6 子任务 6/10 (ID: 2rjSVNgnq4KS8BC5BeoCobg):
2025-08-02 15:59:24,209 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:24,209 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 15:59:25,016 - SimulationService - INFO - 📄 任务 6 子任务 7/10 (ID: 3Z9yIf4hs4l6bLs18FuAA0S9):
2025-08-02 15:59:25,017 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:25,017 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 15:59:25,871 - SimulationService - INFO - 📄 任务 6 子任务 8/10 (ID: 1oFP7J8pn56sb34ERstWkSv):
2025-08-02 15:59:25,871 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 15:59:26,767 - SimulationService - INFO - 📄 任务 6 子任务 9/10 (ID: 4wFXTLbhz4rAagT10l3K5Tm4):
2025-08-02 15:59:26,767 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 15:59:27,601 - SimulationService - INFO - 📄 任务 6 子任务 10/10 (ID: 1TqjSW1YU4Z88WVoRRAKYVH):
2025-08-02 15:59:27,601 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 15:59:28,576 - SimulationService - INFO - 任务 11 提交成功
2025-08-02 15:59:28,930 - SimulationService - ERROR - ❌ 任务 7 失败，包含的Alpha表达式：
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 120), Decay: 0
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 240), Decay: 0
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 5, f=0.5), Decay: 0
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 22, f=0.5), Decay: 0
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 66, f=0.5), Decay: 0
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 120, f=0.5), Decay: 0
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 240, f=0.5), Decay: 0
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 5, f=2), Decay: 0
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 22, f=2), Decay: 0
2025-08-02 15:59:28,930 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 66, f=2), Decay: 0
2025-08-02 15:59:28,931 - SimulationService - WARNING - ❌ 任务 7 失败https://api.worldquantbrain.com/simulations/1TxzN8fhp4E1cht1aSh991OT: {"children":["4lGlNfger5gkaxkevMGK3MJ","1Q1ULp2XG5fJ8Fv14ndPeVxY","4lqhiU9mZ55Cb9cDZkQkXjz","14N2da2c75aO9iC4UVMjS0w","2dQkIgdFi4Jo8AXXBJtlLLb","1frjiN5ki4JY8BX15a40Kpt7","kXdKWfEn57T9J4GES0jGdx","22XPTdbCO56mb6nmAZ364wW","2496eG10H4Rp9JEc4Ose938","1kxCiFgLd5ghcps17u6Z1VDk"],"type":"REGULAR","status":"ERROR"}
2025-08-02 15:59:28,931 - SimulationService - INFO - 正在查询任务 7 的 10 个子任务详情...
2025-08-02 15:59:29,339 - SimulationService - INFO - 📄 任务 7 子任务 1/10 (ID: 4lGlNfger5gkaxkevMGK3MJ):
2025-08-02 15:59:29,339 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 15:59:30,261 - SimulationService - INFO - 📄 任务 7 子任务 2/10 (ID: 1Q1ULp2XG5fJ8Fv14ndPeVxY):
2025-08-02 15:59:30,261 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 15:59:31,184 - SimulationService - INFO - 📄 任务 7 子任务 3/10 (ID: 4lqhiU9mZ55Cb9cDZkQkXjz):
2025-08-02 15:59:31,184 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:31,184 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 15:59:32,104 - SimulationService - INFO - 📄 任务 7 子任务 4/10 (ID: 14N2da2c75aO9iC4UVMjS0w):
2025-08-02 15:59:32,104 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:32,105 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 15:59:32,982 - SimulationService - INFO - 📄 任务 7 子任务 5/10 (ID: 2dQkIgdFi4Jo8AXXBJtlLLb):
2025-08-02 15:59:32,982 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:32,982 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 15:59:33,880 - SimulationService - INFO - 📄 任务 7 子任务 6/10 (ID: 1frjiN5ki4JY8BX15a40Kpt7):
2025-08-02 15:59:33,880 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:33,880 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 15:59:34,717 - SimulationService - INFO - 📄 任务 7 子任务 7/10 (ID: kXdKWfEn57T9J4GES0jGdx):
2025-08-02 15:59:34,718 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:34,718 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 15:59:35,630 - SimulationService - INFO - 📄 任务 7 子任务 8/10 (ID: 22XPTdbCO56mb6nmAZ364wW):
2025-08-02 15:59:35,630 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:35,630 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 15:59:36,508 - SimulationService - INFO - 📄 任务 7 子任务 9/10 (ID: 2496eG10H4Rp9JEc4Ose938):
2025-08-02 15:59:36,508 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:36,509 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 15:59:37,323 - SimulationService - INFO - 📄 任务 7 子任务 10/10 (ID: 1kxCiFgLd5ghcps17u6Z1VDk):
2025-08-02 15:59:37,323 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:37,323 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 15:59:38,255 - SimulationService - INFO - 任务 12 提交成功
2025-08-02 15:59:38,660 - SimulationService - ERROR - ❌ 任务 8 失败，包含的Alpha表达式：
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 120, f=2), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 240, f=2), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 66), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 120), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 240), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(ts_mean(close, 10), 120), std=4), 66), Decay: 0
2025-08-02 15:59:38,661 - SimulationService - WARNING - ❌ 任务 8 失败https://api.worldquantbrain.com/simulations/2x01ir3z74m0aulAINlfw4R: {"children":["eaOAL9rU4Ss9SM9z0jSxIu","aKakG75g4il9Ya7jhAPSiH","2vKbdSciT4HB9vspheDvM6Z","3v13YUaXb4t39uGQiptqKGG","sMYtAb3C4FS9wra1sDHSyu","2GRksS85a57RbGMlfjyTfyL","1DOxZkg1u4IkctZ9PjMKtlD","3lTzbW47B5iEbkevnHOmz60","4qNoa35FY4qt9vUImZi3BPg","2ruIVS2iU4IKcjqE2gcm0Wn"],"type":"REGULAR","status":"ERROR"}
2025-08-02 15:59:38,661 - SimulationService - INFO - 正在查询任务 8 的 10 个子任务详情...
2025-08-02 15:59:39,072 - SimulationService - INFO - 📄 任务 8 子任务 1/10 (ID: eaOAL9rU4Ss9SM9z0jSxIu):
2025-08-02 15:59:39,072 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:39,072 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 15:59:40,038 - SimulationService - INFO - 📄 任务 8 子任务 2/10 (ID: aKakG75g4il9Ya7jhAPSiH):
2025-08-02 15:59:40,038 - SimulationService - INFO -    状态: ERROR
2025-08-02 15:59:40,038 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 15:59:40,918 - SimulationService - INFO - 📄 任务 8 子任务 3/10 (ID: 2vKbdSciT4HB9vspheDvM6Z):
2025-08-02 15:59:40,919 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 15:59:41,868 - SimulationService - INFO - 📄 任务 8 子任务 4/10 (ID: 3v13YUaXb4t39uGQiptqKGG):
2025-08-02 15:59:41,869 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 15:59:42,685 - SimulationService - INFO - 📄 任务 8 子任务 5/10 (ID: sMYtAb3C4FS9wra1sDHSyu):
2025-08-02 15:59:42,686 - SimulationService - INFO -    状态: CANCELLED
