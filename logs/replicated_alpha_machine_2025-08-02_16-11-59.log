2025-08-02 16:11:59,671 - ReplicatedAlphaMachine - INFO - 日志系统初始化完成，日志文件: logs/replicated_alpha_machine_2025-08-02_16-11-59.log
2025-08-02 16:11:59,671 - ReplicatedAlphaMachine - INFO - 加载配置文件
2025-08-02 16:11:59,672 - root - INFO - dataset_id: model26
2025-08-02 16:11:59,672 - root - INFO - region: USA
2025-08-02 16:11:59,672 - root - INFO - universe: TOP3000
2025-08-02 16:11:59,672 - root - INFO - delay: 1
2025-08-02 16:11:59,672 - root - INFO - decay: 0
2025-08-02 16:11:59,672 - root - INFO - neutralization: SUBINDUSTRY
2025-08-02 16:11:59,672 - root - INFO - prefix: winsorize
2025-08-02 16:11:59,672 - root - INFO - coverage: 0.8
2025-08-02 16:11:59,672 - root - INFO - truncation: 0.08
2025-08-02 16:11:59,672 - root - INFO - instrument_type: EQUITY
2025-08-02 16:11:59,672 - root - INFO - pasteurization: ON
2025-08-02 16:11:59,672 - root - INFO - nan_handling: OFF
2025-08-02 16:11:59,672 - root - INFO - unit_handling: VERIFY
2025-08-02 16:11:59,672 - root - INFO - language: FASTEXPR
2025-08-02 16:11:59,672 - root - INFO - visualization: false
2025-08-02 16:11:59,672 - root - INFO - max_trade: OFF
2025-08-02 16:11:59,672 - root - INFO - test_period: P0Y
2025-08-02 16:11:59,672 - root - INFO - events: open_events
2025-08-02 16:11:59,672 - root - INFO - alpha_num_filter: 2000
2025-08-02 16:11:59,672 - root - INFO - alpha_num_submit: 500
2025-08-02 16:11:59,672 - root - INFO - third_promote_num: 200
2025-08-02 16:11:59,672 - root - INFO - batch_size: 500
2025-08-02 16:11:59,672 - root - INFO - second_promote_sharp: 1.2
2025-08-02 16:11:59,672 - root - INFO - second_promote_fitness: 0.7
2025-08-02 16:11:59,672 - root - INFO - third_promote_sharp: 1.3
2025-08-02 16:11:59,672 - root - INFO - third_promote_fitness: 0.8
2025-08-02 16:11:59,672 - root - INFO - submit_sharp_th: 1.58
2025-08-02 16:11:59,672 - root - INFO - submit_fitness_th: 1.0
2025-08-02 16:11:59,672 - root - INFO - submit_margin_th: 1.0
2025-08-02 16:11:59,672 - root - INFO - is_simulate: True
2025-08-02 16:11:59,672 - root - INFO - is_second_promote: True
2025-08-02 16:11:59,672 - root - INFO - is_third_promote: True
2025-08-02 16:11:59,672 - root - INFO - is_prune: False
2025-08-02 16:11:59,672 - root - INFO - fields_file: filtered_shuffled_expressions.txt
2025-08-02 16:11:59,672 - root - INFO - is_filter_pnl: False
2025-08-02 16:11:59,673 - root - INFO - is_preprocess_alpha: True
2025-08-02 16:11:59,673 - ReplicatedAlphaMachine - INFO - 初始化数据库服务
2025-08-02 16:11:59,673 - root - INFO - 初始化数据库连接
2025-08-02 16:11:59,674 - root - INFO - 数据库连接成功
2025-08-02 16:11:59,679 - root - INFO - 数据库表创建/检查完成
2025-08-02 16:11:59,680 - root - INFO - check_status 字段已存在，跳过迁移
2025-08-02 16:11:59,680 - root - INFO - 数据库初始化完成
2025-08-02 16:11:59,680 - root - INFO - 创建异步数据库服务
2025-08-02 16:11:59,681 - AsyncDatabaseService - INFO - 异步数据库服务已初始化，线程池大小: 3
2025-08-02 16:11:59,681 - ReplicatedAlphaMachine - INFO - 初始化认证服务
2025-08-02 16:11:59,682 - ReplicatedAlphaMachine - INFO - 初始化业务服务
2025-08-02 16:11:59,682 - ReplicatedAlphaMachine - INFO - Alpha机器初始化完成
2025-08-02 16:11:59,682 - ReplicatedAlphaMachine - INFO - 开始Alpha生成和仿真流程
2025-08-02 16:11:59,682 - ReplicatedAlphaMachine - INFO - 开始生成Alpha表达式
2025-08-02 16:11:59,682 - AlphaService - INFO - 从文件 new_test_factors.txt 创建Alpha
2025-08-02 16:11:59,683 - AlphaService - INFO - 从文件加载了 5 个数据字段
2025-08-02 16:11:59,683 - AlphaFactory - INFO - 生成了 565 个一阶Alpha表达式
2025-08-02 16:11:59,683 - AlphaService - INFO - 生成了 565 个Alpha表达式
2025-08-02 16:11:59,683 - ReplicatedAlphaMachine - INFO - 生成了 565 个Alpha，开始过滤已回测的Alpha
2025-08-02 16:11:59,685 - root - INFO - 已过滤掉 0 个已回测的alpha，剩余 565 个新alpha待回测
2025-08-02 16:11:59,686 - ReplicatedAlphaMachine - INFO - 过滤后剩余 565 个未回测的Alpha
2025-08-02 16:11:59,686 - ReplicatedAlphaMachine - INFO - 执行日期范围: 12-01 到 12-07
2025-08-02 16:11:59,686 - ReplicatedAlphaMachine - INFO - 开始批量仿真 565 个Alpha
2025-08-02 16:11:59,686 - SimulationService - INFO - 开始批量仿真，批次大小: 500
2025-08-02 16:11:59,686 - SimulationService - INFO - 进行第 1/2 批仿真，数量: 500
2025-08-02 16:11:59,686 - SimulationService - INFO - 开始仿真 500 个Alpha
2025-08-02 16:11:59,686 - SimulationService - INFO - 准备仿真 50 个任务
2025-08-02 16:12:02,046 - root - INFO - 登录成功
2025-08-02 16:12:02,046 - root - INFO - 响应内容: {"user":{"id":"YK49234"},"token":{"expiry":14400.0},"permissions":["BEFORE_AND_AFTER_PERFORMANCE_V2","BRAIN_LABS","BRAIN_LABS_JUPYTER_LAB","CONSULTANT","MULTI_SIMULATION","PROD_ALPHAS","REFERRAL","VISUALIZATION","WORKDAY"]}
2025-08-02 16:12:02,567 - SimulationService - INFO - 任务 0 提交成功
2025-08-02 16:12:03,034 - SimulationService - INFO - 任务 1 提交成功
2025-08-02 16:12:03,488 - SimulationService - INFO - 任务 2 提交成功
2025-08-02 16:12:03,921 - SimulationService - INFO - 任务 3 提交成功
2025-08-02 16:12:04,411 - SimulationService - INFO - 任务 4 提交成功
2025-08-02 16:12:06,367 - SimulationService - INFO - 任务 5 提交成功
2025-08-02 16:12:06,793 - SimulationService - INFO - 任务 6 提交成功
2025-08-02 16:12:07,183 - SimulationService - INFO - 任务 7 提交成功
2025-08-02 16:12:14,753 - SimulationService - ERROR - ❌ 任务 6 失败，包含的Alpha表达式：
2025-08-02 16:12:14,753 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120, driver='gaussian'), Decay: 0
2025-08-02 16:12:14,753 - SimulationService - ERROR -    - Alpha: ts_quantile(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240, driver='gaussian'), Decay: 0
2025-08-02 16:12:14,753 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 16:12:14,753 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 16:12:14,753 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 66), Decay: 0
2025-08-02 16:12:14,753 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120), Decay: 0
2025-08-02 16:12:14,753 - SimulationService - ERROR -    - Alpha: ts_entropy(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240), Decay: 0
2025-08-02 16:12:14,754 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 16:12:14,754 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 16:12:14,754 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 66), Decay: 0
2025-08-02 16:12:14,754 - SimulationService - WARNING - ❌ 任务 6 失败https://api.worldquantbrain.com/simulations/2jh1dbcZQ55jbuKlnk3Kd1n: {"children":["2Lt4UW2RV4p59sK3hq0Maah","jHZPEgb64zsaB82LpKVGE4","iiYmZ1C4MHc4Y6iD4DRA","403OUM5vP5eNaF1Vu5lsNMC","2tYkLI9ep4vI9IftgFEOCWm","sP8WtgIo4H1bG71dSJP4Mlp","3x1c97e5X4OXbGmIwwiZUNg","Adc9b7Tr4Fucna7srRh3zT","30hU5m56L4qE8S92rSOGPVs","2wlr4Iglw4vXaF7EvGLJZM6"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:12:14,754 - SimulationService - INFO - 正在查询任务 6 的 10 个子任务详情...
2025-08-02 16:12:15,173 - SimulationService - INFO - 📄 任务 6 子任务 1/10 (ID: 2Lt4UW2RV4p59sK3hq0Maah):
2025-08-02 16:12:15,173 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:16,084 - SimulationService - INFO - 📄 任务 6 子任务 2/10 (ID: jHZPEgb64zsaB82LpKVGE4):
2025-08-02 16:12:16,085 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:17,312 - SimulationService - INFO - 📄 任务 6 子任务 3/10 (ID: iiYmZ1C4MHc4Y6iD4DRA):
2025-08-02 16:12:17,312 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:17,312 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:12:18,134 - SimulationService - INFO - 📄 任务 6 子任务 4/10 (ID: 403OUM5vP5eNaF1Vu5lsNMC):
2025-08-02 16:12:18,134 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:18,135 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:12:19,055 - SimulationService - INFO - 📄 任务 6 子任务 5/10 (ID: 2tYkLI9ep4vI9IftgFEOCWm):
2025-08-02 16:12:19,055 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:19,055 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:12:20,077 - SimulationService - INFO - 📄 任务 6 子任务 6/10 (ID: sP8WtgIo4H1bG71dSJP4Mlp):
2025-08-02 16:12:20,077 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:20,077 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:12:20,998 - SimulationService - INFO - 📄 任务 6 子任务 7/10 (ID: 3x1c97e5X4OXbGmIwwiZUNg):
2025-08-02 16:12:20,999 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:20,999 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_entropy"
2025-08-02 16:12:21,920 - SimulationService - INFO - 📄 任务 6 子任务 8/10 (ID: Adc9b7Tr4Fucna7srRh3zT):
2025-08-02 16:12:21,920 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:22,786 - SimulationService - INFO - 📄 任务 6 子任务 9/10 (ID: 30hU5m56L4qE8S92rSOGPVs):
2025-08-02 16:12:22,787 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:23,730 - SimulationService - INFO - 📄 任务 6 子任务 10/10 (ID: 2wlr4Iglw4vXaF7EvGLJZM6):
2025-08-02 16:12:23,730 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:24,729 - SimulationService - INFO - 任务 8 提交成功
2025-08-02 16:12:25,041 - SimulationService - ERROR - ❌ 任务 7 失败，包含的Alpha表达式：
2025-08-02 16:12:25,041 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120), Decay: 0
2025-08-02 16:12:25,041 - SimulationService - ERROR -    - Alpha: ts_av_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240), Decay: 0
2025-08-02 16:12:25,041 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5, f=0.5), Decay: 0
2025-08-02 16:12:25,041 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22, f=0.5), Decay: 0
2025-08-02 16:12:25,041 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 66, f=0.5), Decay: 0
2025-08-02 16:12:25,042 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120, f=0.5), Decay: 0
2025-08-02 16:12:25,042 - SimulationService - ERROR -    - Alpha: ts_min_max_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240, f=0.5), Decay: 0
2025-08-02 16:12:25,042 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5, f=2), Decay: 0
2025-08-02 16:12:25,042 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22, f=2), Decay: 0
2025-08-02 16:12:25,042 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 66, f=2), Decay: 0
2025-08-02 16:12:25,042 - SimulationService - WARNING - ❌ 任务 7 失败https://api.worldquantbrain.com/simulations/43uuDFf5s54EafU7ykmR0wn: {"children":["Ia296fmh4DnaAZ4dBlROsp","2wUilb2lc4oC97rHHxa6FvU","tKLT93F34Dwbke15fRYwNEi","3E9Er3fBb4It8ztsN5Ewio0","3VqTY19Vh56RblzRD2Pj3Cm","xqTK48Wv4UAcyaQYOrV5rH","3kJCFZ5Cq4NZa9IoWECEWzp","4fxywpbjX4B19lN19BN5yrGH","1Bbveo2Jp4wF9er12ERMwwAI","14WEsgbJx4Zdck6hvo24I72"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:12:25,042 - SimulationService - INFO - 正在查询任务 7 的 10 个子任务详情...
2025-08-02 16:12:25,402 - SimulationService - INFO - 📄 任务 7 子任务 1/10 (ID: Ia296fmh4DnaAZ4dBlROsp):
2025-08-02 16:12:25,402 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:26,217 - SimulationService - INFO - 📄 任务 7 子任务 2/10 (ID: 2wUilb2lc4oC97rHHxa6FvU):
2025-08-02 16:12:26,217 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:27,042 - SimulationService - INFO - 📄 任务 7 子任务 3/10 (ID: tKLT93F34Dwbke15fRYwNEi):
2025-08-02 16:12:27,042 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:27,042 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:12:27,883 - SimulationService - INFO - 📄 任务 7 子任务 4/10 (ID: 3E9Er3fBb4It8ztsN5Ewio0):
2025-08-02 16:12:27,884 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:27,884 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:12:28,735 - SimulationService - INFO - 📄 任务 7 子任务 5/10 (ID: 3VqTY19Vh56RblzRD2Pj3Cm):
2025-08-02 16:12:28,735 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:28,735 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:12:29,547 - SimulationService - INFO - 📄 任务 7 子任务 6/10 (ID: xqTK48Wv4UAcyaQYOrV5rH):
2025-08-02 16:12:29,547 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:29,547 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:12:30,367 - SimulationService - INFO - 📄 任务 7 子任务 7/10 (ID: 3kJCFZ5Cq4NZa9IoWECEWzp):
2025-08-02 16:12:30,367 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:30,367 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_diff"
2025-08-02 16:12:31,407 - SimulationService - INFO - 📄 任务 7 子任务 8/10 (ID: 4fxywpbjX4B19lN19BN5yrGH):
2025-08-02 16:12:31,408 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:31,408 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:12:32,262 - SimulationService - INFO - 📄 任务 7 子任务 9/10 (ID: 1Bbveo2Jp4wF9er12ERMwwAI):
2025-08-02 16:12:32,263 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:32,263 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:12:33,106 - SimulationService - INFO - 📄 任务 7 子任务 10/10 (ID: 14WEsgbJx4Zdck6hvo24I72):
2025-08-02 16:12:33,106 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:33,106 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:12:34,045 - SimulationService - INFO - 任务 9 提交成功
2025-08-02 16:12:34,379 - SimulationService - ERROR - ❌ 任务 8 失败，包含的Alpha表达式：
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120, f=2), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_min_max_cps(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240, f=2), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 66), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_count_nans(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 66), Decay: 0
2025-08-02 16:12:34,379 - SimulationService - WARNING - ❌ 任务 8 失败https://api.worldquantbrain.com/simulations/1BhwMe7684zxb9AqkZ8TEx: {"children":["1pTOiKfgZ4Im9G2NeDiAqoI","2vpl1m7t84oycL3lG6G7e7Z","4a7CuaeE54ZbgTMfZWzOxV","4897rcaii4Sd9skTGkCAf4K","eSxz8204tc9mxRtOFlDe2","195tXOc7g53z8xQCiUkKWRk","2PBx3I6wz4zH9QP1crhYkpVk","2MckyI6bK4Xd8AS486GytlO","4xiFXlewb4pyb6Hljbd2iTO","4cpvqpe7H4LibH5i6pgTBwh"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:12:34,379 - SimulationService - INFO - 正在查询任务 8 的 10 个子任务详情...
2025-08-02 16:12:34,705 - SimulationService - INFO - 📄 任务 8 子任务 1/10 (ID: 1pTOiKfgZ4Im9G2NeDiAqoI):
2025-08-02 16:12:34,705 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:34,705 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:12:35,557 - SimulationService - INFO - 📄 任务 8 子任务 2/10 (ID: 2vpl1m7t84oycL3lG6G7e7Z):
2025-08-02 16:12:35,557 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:35,557 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_max_cps"
2025-08-02 16:12:36,460 - SimulationService - INFO - 📄 任务 8 子任务 3/10 (ID: 4a7CuaeE54ZbgTMfZWzOxV):
2025-08-02 16:12:36,461 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:37,396 - SimulationService - INFO - 📄 任务 8 子任务 4/10 (ID: 4897rcaii4Sd9skTGkCAf4K):
2025-08-02 16:12:37,397 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:38,254 - SimulationService - INFO - 📄 任务 8 子任务 5/10 (ID: eSxz8204tc9mxRtOFlDe2):
2025-08-02 16:12:38,254 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:39,227 - SimulationService - INFO - 📄 任务 8 子任务 6/10 (ID: 195tXOc7g53z8xQCiUkKWRk):
2025-08-02 16:12:39,228 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:40,091 - SimulationService - INFO - 📄 任务 8 子任务 7/10 (ID: 2PBx3I6wz4zH9QP1crhYkpVk):
2025-08-02 16:12:40,092 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:40,966 - SimulationService - INFO - 📄 任务 8 子任务 8/10 (ID: 2MckyI6bK4Xd8AS486GytlO):
2025-08-02 16:12:40,966 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:40,966 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:12:41,991 - SimulationService - INFO - 📄 任务 8 子任务 9/10 (ID: 4xiFXlewb4pyb6Hljbd2iTO):
2025-08-02 16:12:41,992 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:41,992 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:12:42,819 - SimulationService - INFO - 📄 任务 8 子任务 10/10 (ID: 4cpvqpe7H4LibH5i6pgTBwh):
2025-08-02 16:12:42,820 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:42,820 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:12:43,952 - SimulationService - INFO - 任务 10 提交成功
2025-08-02 16:12:44,349 - SimulationService - ERROR - ❌ 任务 9 失败，包含的Alpha表达式：
2025-08-02 16:12:44,349 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120), Decay: 0
2025-08-02 16:12:44,349 - SimulationService - ERROR -    - Alpha: ts_min_diff(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240), Decay: 0
2025-08-02 16:12:44,349 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5, dense=false), Decay: 0
2025-08-02 16:12:44,349 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22, dense=false), Decay: 0
2025-08-02 16:12:44,350 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 66, dense=false), Decay: 0
2025-08-02 16:12:44,350 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120, dense=false), Decay: 0
2025-08-02 16:12:44,350 - SimulationService - ERROR -    - Alpha: ts_decay_linear(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240, dense=false), Decay: 0
2025-08-02 16:12:44,350 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5, lag=0, rettype=0), Decay: 0
2025-08-02 16:12:44,350 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22, lag=0, rettype=0), Decay: 0
2025-08-02 16:12:44,350 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 66, lag=0, rettype=0), Decay: 0
2025-08-02 16:12:44,350 - SimulationService - WARNING - ❌ 任务 9 失败https://api.worldquantbrain.com/simulations/1RHsb94jl4WSbACR5NBwAAK: {"children":["dPHj2aXd4j2b3m7rIB8jAO","29c6eR23C57O9QEmsIpZKkR","BahEM4J251CaxM2QY50L44","2hMa6tfTi4DAaRLga5J10Lh","2YsyNp774kCclFczXq1FwQ","bRg1F25955m9Rh17kVexmTf","2YeXMK8bE5378WuZvqECFvu","4s7K0MgDw4zp9JhOth86ulV","GkUoueMB5jEauZ5EJ4ZcWj","3BIl31fx14wga2I1aiU3vmNu"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:12:44,350 - SimulationService - INFO - 正在查询任务 9 的 10 个子任务详情...
2025-08-02 16:12:44,858 - SimulationService - INFO - 📄 任务 9 子任务 1/10 (ID: dPHj2aXd4j2b3m7rIB8jAO):
2025-08-02 16:12:44,858 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:44,858 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:12:46,332 - SimulationService - INFO - 📄 任务 9 子任务 2/10 (ID: 29c6eR23C57O9QEmsIpZKkR):
2025-08-02 16:12:46,332 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:46,332 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_min_diff"
2025-08-02 16:12:47,214 - SimulationService - INFO - 📄 任务 9 子任务 3/10 (ID: BahEM4J251CaxM2QY50L44):
2025-08-02 16:12:47,214 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:48,025 - SimulationService - INFO - 📄 任务 9 子任务 4/10 (ID: 2hMa6tfTi4DAaRLga5J10Lh):
2025-08-02 16:12:48,025 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:48,953 - SimulationService - INFO - 📄 任务 9 子任务 5/10 (ID: 2YsyNp774kCclFczXq1FwQ):
2025-08-02 16:12:48,953 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:49,875 - SimulationService - INFO - 📄 任务 9 子任务 6/10 (ID: bRg1F25955m9Rh17kVexmTf):
2025-08-02 16:12:49,875 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:50,847 - SimulationService - INFO - 📄 任务 9 子任务 7/10 (ID: 2YeXMK8bE5378WuZvqECFvu):
2025-08-02 16:12:50,847 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:51,733 - SimulationService - INFO - 📄 任务 9 子任务 8/10 (ID: 4s7K0MgDw4zp9JhOth86ulV):
2025-08-02 16:12:51,743 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:52,640 - SimulationService - INFO - 📄 任务 9 子任务 9/10 (ID: GkUoueMB5jEauZ5EJ4ZcWj):
2025-08-02 16:12:52,640 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:53,695 - SimulationService - INFO - 📄 任务 9 子任务 10/10 (ID: 3BIl31fx14wga2I1aiU3vmNu):
2025-08-02 16:12:53,696 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:54,627 - SimulationService - INFO - 任务 11 提交成功
2025-08-02 16:12:54,935 - SimulationService - ERROR - ❌ 任务 10 失败，包含的Alpha表达式：
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120, lag=0, rettype=0), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_regression(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240, lag=0, rettype=0), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 66), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 120), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_skewness(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 240), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_target_tvr_decay(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), lambda_min=0, lambda_max=1, target_tvr=0.1), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_product(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 5), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - ERROR -    - Alpha: ts_product(winsorize(ts_backfill(ts_max(close, 10), 120), std=4), 22), Decay: 0
2025-08-02 16:12:54,936 - SimulationService - WARNING - ❌ 任务 10 失败https://api.worldquantbrain.com/simulations/1ZogIR7524tIangJT7OXGTP: {"children":["1nXAD6aGe4P4c8Mb2FUYvRJ","3cqXrCa6A4taba210RMNN4r6","3dbgfM68Z4lnc8DLOG3tNZ","4pYLJ6cPE4Kw8EEbW13HUl4","2ymNvFdTc4YK9LJrhdym59q","4cqAUx6Gw4Tda9RJenH170n","W2rTN3Sf55xcxEKEpowFwO","uG7r5BY5f1aIlwjauOGvT","PaoFqdqW4Xaa254WICGwZH","2VoUm15Zn5ii9LG15ZJS5d7h"],"type":"REGULAR","status":"ERROR"}
2025-08-02 16:12:54,936 - SimulationService - INFO - 正在查询任务 10 的 10 个子任务详情...
2025-08-02 16:12:55,260 - SimulationService - INFO - 📄 任务 10 子任务 1/10 (ID: 1nXAD6aGe4P4c8Mb2FUYvRJ):
2025-08-02 16:12:55,261 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:56,122 - SimulationService - INFO - 📄 任务 10 子任务 2/10 (ID: 3cqXrCa6A4taba210RMNN4r6):
2025-08-02 16:12:56,122 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:12:56,944 - SimulationService - INFO - 📄 任务 10 子任务 3/10 (ID: 3dbgfM68Z4lnc8DLOG3tNZ):
2025-08-02 16:12:56,944 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:56,944 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:12:57,853 - SimulationService - INFO - 📄 任务 10 子任务 4/10 (ID: 4pYLJ6cPE4Kw8EEbW13HUl4):
2025-08-02 16:12:57,853 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:57,853 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:12:58,786 - SimulationService - INFO - 📄 任务 10 子任务 5/10 (ID: 2ymNvFdTc4YK9LJrhdym59q):
2025-08-02 16:12:58,787 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:58,787 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:12:59,600 - SimulationService - INFO - 📄 任务 10 子任务 6/10 (ID: 4cqAUx6Gw4Tda9RJenH170n):
2025-08-02 16:12:59,601 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:12:59,601 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:13:00,525 - SimulationService - INFO - 📄 任务 10 子任务 7/10 (ID: W2rTN3Sf55xcxEKEpowFwO):
2025-08-02 16:13:00,525 - SimulationService - INFO -    状态: ERROR
2025-08-02 16:13:00,525 - SimulationService - ERROR -    错误信息: Attempted to use inaccessible or unknown operator "ts_skewness"
2025-08-02 16:13:01,356 - SimulationService - INFO - 📄 任务 10 子任务 8/10 (ID: uG7r5BY5f1aIlwjauOGvT):
2025-08-02 16:13:01,357 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:13:02,269 - SimulationService - INFO - 📄 任务 10 子任务 9/10 (ID: PaoFqdqW4Xaa254WICGwZH):
2025-08-02 16:13:02,269 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:13:03,151 - SimulationService - INFO - 📄 任务 10 子任务 10/10 (ID: 2VoUm15Zn5ii9LG15ZJS5d7h):
2025-08-02 16:13:03,151 - SimulationService - INFO -    状态: CANCELLED
2025-08-02 16:13:04,092 - SimulationService - INFO - 任务 12 提交成功
2025-08-02 16:13:05,845 - ReplicatedAlphaMachine - INFO - 开始清理资源
2025-08-02 16:13:05,846 - ReplicatedAlphaMachine - INFO - 等待异步数据库操作完成
2025-08-02 16:13:05,847 - AsyncDatabaseService - INFO - 所有异步数据库操作已完成
2025-08-02 16:13:05,847 - AsyncDatabaseService - INFO - 开始关闭异步数据库服务
2025-08-02 16:13:05,847 - AsyncDatabaseService - INFO - 线程池已关闭
2025-08-02 16:13:05,852 - AsyncDatabaseService - INFO - 所有线程数据库连接已关闭
2025-08-02 16:13:05,852 - AsyncDatabaseService - INFO - 异步数据库服务已关闭
2025-08-02 16:13:05,852 - ReplicatedAlphaMachine - INFO - 数据库连接已关闭
2025-08-02 16:13:05,852 - ReplicatedAlphaMachine - INFO - 资源清理完成
